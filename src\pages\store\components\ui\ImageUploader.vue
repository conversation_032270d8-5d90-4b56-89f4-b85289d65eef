<template>
  <view class="image-uploader">
    <view v-if="!multiple" class="upload-box" @click="handleUpload">
      <image 
        v-if="modelValue && modelValue.length > 0" 
        :src="Array.isArray(modelValue) ? modelValue[0] : modelValue" 
        class="upload-image" 
        mode="aspectFill"
      ></image>
      <view v-else class="upload-placeholder">
        <text class="upload-icon">+</text>
        <text class="upload-text">{{ placeholder }}</text>
      </view>
    </view>

    <view v-else class="upload-grid">
      <view 
        v-for="(image, index) in normalizedImages" 
        :key="index"
        class="grid-item"
      >
        <image :src="image" mode="aspectFill" class="grid-image"></image>
        <text class="delete-icon" @click="handleDelete(index)">×</text>
      </view>
      
      <view 
        v-if="normalizedImages.length < maxCount"
        class="grid-item add" 
        @click="handleUpload"
      >
        <text class="add-icon">+</text>
        <text class="add-text" v-if="normalizedImages.length === 0">{{ placeholder }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

// 定义props
const props = defineProps({
  modelValue: {
    type: [String, Array],
    default: () => ''
  },
  multiple: {
    type: Boolean,
    default: false
  },
  maxCount: {
    type: Number,
    default: 3
  },
  placeholder: {
    type: String,
    default: '点击上传照片'
  }
})

// 定义emits
const emit = defineEmits(['update:modelValue', 'delete'])

// 计算属性
const normalizedImages = computed(() => {
  // Ensure we always work with an array for multiple mode
  if (props.multiple) {
    if (Array.isArray(props.modelValue)) {
      return props.modelValue;
    }
    // If we received a string or undefined, normalize to array
    return props.modelValue ? [props.modelValue] : [];
  }
  // For single mode, still return an array for consistency
  if (Array.isArray(props.modelValue)) {
    return props.modelValue;
  }
  return props.modelValue ? [props.modelValue] : [];
})

// 方法
const handleUpload = () => {
  uni.chooseImage({
    count: props.multiple ? (props.maxCount - normalizedImages.value.length) : 1,
    success: (res) => {
      if (props.multiple) {
        // For multiple mode, add all selected images
        const newImages = [...normalizedImages.value, ...res.tempFilePaths];
        console.log('Updating images array:', newImages);
        emit('update:modelValue', newImages);
      } else {
        // For single mode, just use the first image
        console.log('Updating single image:', res.tempFilePaths[0]);
        emit('update:modelValue', res.tempFilePaths[0]);
      }
    }
  })
}

const handleDelete = (index) => {
  if (props.multiple) {
    console.log('Deleting image at index:', index);
    emit('delete', index);
  } else {
    emit('update:modelValue', '');
  }
}
</script>

<style scoped>
.upload-box {
  width: 240rpx;
  height: 240rpx;
  border: 2rpx dashed #ddd;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background-color: #fafafa;
}

.upload-box:active {
  background-color: #f0f0f0;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 60rpx;
  color: var(--primary-color);
  line-height: 1;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.grid-item {
  position: relative;
  width: 180rpx;
  height: 180rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  overflow: hidden;
  background-color: #fafafa;
}

.grid-item.add {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ddd;
}

.add-icon {
  font-size: 60rpx;
  color: var(--primary-color);
  line-height: 1;
  margin-bottom: 6rpx;
}

.add-text {
  font-size: 22rpx;
  color: #999;
  text-align: center;
  padding: 0 10rpx;
}

.grid-image {
  width: 100%;
  height: 100%;
}

.delete-icon {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}
</style> 