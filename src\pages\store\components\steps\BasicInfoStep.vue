<template>
  <view class="basic-info">
    <view class="form-section">
      <view class="section-title">
        <view class="title-icon">
          <text class="icon">📋</text>
        </view>
        <text class="title-text">基础信息</text>
      </view>

      <view class="form-grid">
        <view class="form-row">
          <view class="form-field" :class="{ 'has-error': errors.name }">
            <view class="field-label">
              <text class="required">*</text>
              <text>用气户名</text>
            </view>
            <input
              class="field-input"
              v-model.trim="form.name"
              placeholder="请输入用气户名"
              @input="validateField('name')"
              @blur="validateField('name')"
            />
            <text v-if="errors.name" class="error-message">{{ errors.name }}</text>
          </view>

          <view class="form-field" :class="{ 'has-error': errors.type }">
            <view class="field-label">
              <text class="required">*</text>
              <text>用气类型</text>
            </view>
            <picker
              class="field-picker"
              @change="handleTypeChange"
              :value="typeIndex"
              :range="typeOptions"
              range-key="name"
            >
              <view class="picker-content">
                <text>{{ typeOptions[typeIndex]?.name || '请选择用气类型' }}</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
            <text v-if="errors.type" class="error-message">{{ errors.type }}</text>
          </view>
        </view>

        <view class="form-row">
          <view class="form-field" :class="{ 'has-error': errors.location }">
            <view class="field-label">
              <text class="required">*</text>
              <text>用气地址</text>
            </view>
            <view class="location-picker">
              <input
                class="field-input location-input"
                v-model="form.address"
                placeholder="请选择用气地址"
                disabled
                :class="{'has-value': form.address}"
              />
              <view class="location-btn" @click="chooseLocation">
                <text class="location-icon">📍</text>
                <text class="location-text">选择位置</text>
              </view>
            </view>
            <text v-if="errors.location" class="error-message">{{ errors.location }}</text>
          </view>
        </view>

        <view class="form-row">
          <view class="form-field">
            <view class="field-label">
              <text>详细地址</text>
              <text class="optional">(选填)</text>
            </view>
            <input
              class="field-input"
              v-model.trim="form.detailAddress"
              placeholder="请输入详细地址，如门牌号、楼层等"
            />
          </view>
        </view>

        <view class="form-row">
          <view class="form-field" :class="{ 'has-error': errors.phone }">
            <view class="field-label">
              <text class="required">*</text>
              <text>联系电话</text>
            </view>
            <input
              class="field-input"
              v-model.trim="form.phone"
              placeholder="请输入联系电话"
              type="number"
              @input="validateField('phone')"
              @blur="validateField('phone')"
              maxlength="11"
            />
            <text v-if="errors.phone" class="error-message">{{ errors.phone }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 权限授权管理 -->
    <view class="form-section">
      <view class="section-title">
        <view class="title-icon">
          <text class="icon">👥</text>
        </view>
        <text class="title-text">权限授权</text>
        <text class="description">(选填，最多4人)</text>
      </view>

      <view class="auth-users-container">
        <view
          v-for="(user, index) in form.authUsers"
          :key="index"
          class="auth-user-card"
        >
          <view class="card-header">
            <view class="card-info">
              <text class="card-name">{{ user.name }}</text>
              <text class="card-phone">{{ user.phone }}</text>
            </view>
            <view class="card-actions">
              <view class="unlock-badge" :class="{ active: user.canUnlock }">
                <text class="badge-text">{{ user.canUnlock ? '可开锁' : '不可开锁' }}</text>
              </view>
              <view class="icon-btn edit-icon-btn" @click="editAuthUser(index)">
                <text class="icon">✏️</text>
              </view>
              <view class="icon-btn delete-icon-btn" @click="deleteAuthUser(index)">
                <text class="icon">🗑️</text>
              </view>
            </view>
          </view>
        </view>

        <view class="add-user-section" v-if="form.authUsers.length < 4">
          <view class="add-user-btn" @click="showAddUserModal">
            <text class="btn-icon">+</text>
            <text class="btn-text">添加授权用户</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加/编辑用户弹窗 -->
    <view v-if="showUserModal" class="user-modal-mask" @click="closeUserModal">
      <view class="user-modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">{{ editingUserIndex >= 0 ? '编辑' : '添加' }}授权用户</text>
          <view class="modal-close" @click="closeUserModal">
            <text class="close-icon">✕</text>
          </view>
        </view>

        <view class="modal-body">
          <view class="input-group">
            <view class="input-item" :class="{ 'has-error': userFormErrors.name }">
              <view class="input-label">
                <text class="required">*</text>
                <text>用户名</text>
              </view>
              <input
                class="input-field"
                v-model.trim="userForm.name"
                placeholder="请输入用户名"
                @input="validateUserField('name')"
                @blur="validateUserField('name')"
              />
              <text v-if="userFormErrors.name" class="error-msg">{{ userFormErrors.name }}</text>
            </view>

            <view class="input-item" :class="{ 'has-error': userFormErrors.phone }">
              <view class="input-label">
                <text class="required">*</text>
                <text>手机号</text>
              </view>
              <input
                class="input-field"
                v-model.trim="userForm.phone"
                placeholder="请输入手机号"
                type="number"
                maxlength="11"
                @input="validateUserField('phone')"
                @blur="validateUserField('phone')"
              />
              <text v-if="userFormErrors.phone" class="error-msg">{{ userFormErrors.phone }}</text>
            </view>

            <view class="switch-item">
              <view class="switch-label">
                <text>扫码开锁权限</text>
                <text class="switch-hint">开启后该用户可通过扫码开锁</text>
              </view>
              <switch
                class="permission-switch"
                :checked="userForm.canUnlock"
                @change="handleUnlockSwitchChange"
              />
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <button class="btn btn-secondary" @click="closeUserModal">取消</button>
          <button
            class="btn btn-primary"
            :class="{ 'btn-disabled': !isUserFormValid }"
            :disabled="!isUserFormValid"
            @click="saveAuthUser"
          >
            确定
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  typeOptions: {
    type: Array,
    default: () => []
  },
  typeIndex: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'type-change'])

// Reactive data
const errors = reactive({
  name: '',
  location: '',
  phone: '',
  type: ''
})

const showUserModal = ref(false)
const editingUserIndex = ref(-1)

const userForm = reactive({
  name: '',
  phone: '',
  canUnlock: false
})

const userFormErrors = reactive({
  name: '',
  phone: ''
})

// Computed properties
const form = computed({
  get() {
    const formData = { ...props.modelValue };
    if (!formData.location) {
      formData.location = {
        latitude: '',
        longitude: '',
        name: '',
        address: ''
      };
    }
    if (!formData.authUsers) {
      formData.authUsers = [];
    }
    return formData;
  },
  set(value) {
    emit('update:modelValue', value);
  }
})



const isUserFormValid = computed(() => {
  return userForm.name.trim() !== '' &&
         userForm.phone.trim() !== '' &&
         userForm.phone.length === 11 &&
         /^1[3-9]\d{9}$/.test(userForm.phone) &&
         userFormErrors.name === '' &&
         userFormErrors.phone === '';
})
// Lifecycle
onMounted(() => {
  if (!form.value.location) {
    const updatedForm = {
      ...form.value,
      location: {
        latitude: '',
        longitude: '',
        name: '',
        address: ''
      }
    };
    emit('update:modelValue', updatedForm);
  }

  if (!form.value.detailAddress) {
    const updatedForm = { ...form.value, detailAddress: '' };
    emit('update:modelValue', updatedForm);
  }

  if (!form.value.authUsers) {
    const updatedForm = { ...form.value, authUsers: [] };
    emit('update:modelValue', updatedForm);
  }
})

// Methods
const handleTypeChange = (e) => {
  emit('type-change', e.detail.value)
  setTimeout(() => {
    validateField('type');
  }, 0);
}

// 权限授权相关方法
const showAddUserModal = () => {
  // 检查是否已达到最大用户数
  if (form.value.authUsers && form.value.authUsers.length >= 4) {
    uni.showToast({
      title: '最多只能添加4个授权用户',
      icon: 'none',
      duration: 2000
    });
    return;
  }

  editingUserIndex.value = -1;
  Object.assign(userForm, {
    name: '',
    phone: '',
    canUnlock: false
  });
  Object.assign(userFormErrors, {
    name: '',
    phone: ''
  });
  showUserModal.value = true;
}

const editAuthUser = (index) => {
  editingUserIndex.value = index;
  const user = form.value.authUsers[index];
  Object.assign(userForm, {
    name: user.name,
    phone: user.phone,
    canUnlock: user.canUnlock
  });
  Object.assign(userFormErrors, {
    name: '',
    phone: ''
  });
  showUserModal.value = true;
}

const deleteAuthUser = (index) => {
  const user = form.value.authUsers[index];
  uni.showModal({
    title: '确认删除',
    content: `确定要删除授权用户"${user.name}"吗？`,
    success: async (res) => {
      if (res.confirm) {
        // 显示删除加载提示
        uni.showLoading({
          title: '删除中...',
          mask: true
        });

        try {
          // 模拟网络请求延迟
          await new Promise(resolve => setTimeout(resolve, 800));

          const updatedUsers = [...form.value.authUsers];
          updatedUsers.splice(index, 1);
          const updatedForm = { ...form.value, authUsers: updatedUsers };
          emit('update:modelValue', updatedForm);

          uni.showToast({
            title: '删除成功',
            icon: 'success',
            duration: 2000
          });

        } catch (error) {
          console.error('删除用户失败:', error);
          uni.showToast({
            title: '删除失败，请重试',
            icon: 'error',
            duration: 2000
          });
        } finally {
          uni.hideLoading();
        }
      }
    }
  });
}

const closeUserModal = () => {
  showUserModal.value = false;
  editingUserIndex.value = -1;
  Object.assign(userForm, {
    name: '',
    phone: '',
    canUnlock: false
  });
  Object.assign(userFormErrors, {
    name: '',
    phone: ''
  });
}

const handleUnlockSwitchChange = (e) => {
  userForm.canUnlock = e.detail.value;
}

const validateUserField = (field) => {
  switch (field) {
    case 'name':
      if (!userForm.name) {
        userFormErrors.name = '请输入用户名';
      } else if (userForm.name.length < 2) {
        userFormErrors.name = '用户名至少需要2个字符';
      } else if (userForm.name.length > 20) {
        userFormErrors.name = '用户名不能超过20个字符';
      } else {
        userFormErrors.name = '';
      }
      break;

    case 'phone':
      const mobileReg = /^1[3-9]\d{9}$/;
      if (!userForm.phone) {
        userFormErrors.phone = '请输入手机号';
      } else if (userForm.phone.length !== 11) {
        userFormErrors.phone = '手机号必须为11位数字';
      } else if (!mobileReg.test(userForm.phone)) {
        userFormErrors.phone = '请输入有效的手机号格式';
      } else {
        // 检查是否与其他用户重复
        const existingUsers = form.value.authUsers || [];
        const isDuplicate = existingUsers.some((user, index) =>
          user.phone === userForm.phone && index !== editingUserIndex.value
        );
        if (isDuplicate) {
          userFormErrors.phone = '该手机号已存在';
        } else {
          userFormErrors.phone = '';
        }
      }
      break;
  }
  return userFormErrors[field] === '';
}

const validateUserForm = () => {
  validateUserField('name');
  validateUserField('phone');
  return !Object.values(userFormErrors).some(error => error !== '');
}

const saveAuthUser = async () => {
  if (!validateUserForm()) {
    return;
  }

  // 显示加载提示
  uni.showLoading({
    title: editingUserIndex.value >= 0 ? '更新中...' : '添加中...',
    mask: true
  });

  try {
    // 模拟网络请求延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    const updatedUsers = [...(form.value.authUsers || [])];
    const newUser = {
      name: userForm.name,
      phone: userForm.phone,
      canUnlock: userForm.canUnlock
    };

    if (editingUserIndex.value >= 0) {
      // 编辑现有用户
      updatedUsers[editingUserIndex.value] = newUser;
      uni.showToast({
        title: '用户信息更新成功',
        icon: 'success',
        duration: 2000
      });
    } else {
      // 添加新用户
      updatedUsers.push(newUser);
      uni.showToast({
        title: '授权用户添加成功',
        icon: 'success',
        duration: 2000
      });
    }

    const updatedForm = { ...form.value, authUsers: updatedUsers };
    emit('update:modelValue', updatedForm);
    closeUserModal();

  } catch (error) {
    console.error('保存用户失败:', error);
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'error',
      duration: 2000
    });
  } finally {
    uni.hideLoading();
  }
}

const chooseLocation = () => {
  uni.chooseLocation({
    success: (res) => {
      console.log('Location selected:', res);
      // res contains: name, address, latitude, longitude
      const updatedForm = { ...form.value };

      updatedForm.location = {
        latitude: res.latitude,
        longitude: res.longitude,
        name: res.name,
        address: res.address
      };

      // Also update address field for backward compatibility
      updatedForm.address = res.address;

      emit('update:modelValue', updatedForm);
      // 立即清除错误状态
      errors.location = '';
    },
    fail: (err) => {
      console.error('Location selection failed:', err);
      uni.showToast({
        title: '位置选择失败',
        icon: 'none'
      });
    }
  });
}

const validateField = (field) => {
  switch (field) {
    case 'name':
      if (!form.value.name) {
        errors.name = '请输入用气户名';
      } else if (form.value.name.length < 2) {
        errors.name = '用气户名至少需要2个字符';
      } else if (form.value.name.length > 20) {
        errors.name = '用气户名不能超过20个字符';
      } else {
        errors.name = '';
      }
      break;

    case 'location':
      if (!form.value.location ||
          !form.value.location.latitude ||
          !form.value.location.longitude ||
          !form.value.location.address) {
        errors.location = '请选择用气地址';
      } else {
        errors.location = '';
      }
      break;

    case 'phone':
      const mobileReg = /^1[3-9]\d{9}$/;
      const landlineReg = /^(0\d{2,3}-?\d{7,8}|\d{7,8})$/;

      if (!form.value.phone) {
        errors.phone = '请输入联系电话';
      } else if (!mobileReg.test(form.value.phone) && !landlineReg.test(form.value.phone)) {
        errors.phone = '请输入有效的联系电话';
      } else {
        errors.phone = '';
      }
      break;

    case 'type':
      if (!props.typeOptions[props.typeIndex]) {
        errors.type = '请选择用气类型';
      } else {
        errors.type = '';
      }
      break;


  }

  return errors[field] === '';
}

const validateAll = () => {
  validateField('name');
  validateField('location');
  validateField('phone');
  validateField('type');

  const hasErrors = Object.values(errors).some(error => error !== '');

  if (hasErrors) {
    // 显示第一个错误信息
    const firstError = Object.values(errors).find(error => error !== '');
    uni.showToast({
      title: firstError,
      icon: 'none'
    });
    return false;
  }

  return true;
}

// 暴露给父组件的方法
defineExpose({
  validateAll
})
</script>

<style scoped>
/* 组件内部私有样式 */
.basic-info {
  padding: 10rpx;
}

.form-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx 30rpx 50rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* Section title */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  flex-wrap: wrap;
}

.title-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 10rpx;
  background: #f0f5ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.icon {
  font-size: 28rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.description {
  font-size: 24rpx;
  color: #999;
  margin-left: 12rpx;
  font-weight: normal;
}

/* Form grid layout */
.form-grid {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.form-row {
  display: flex;
  gap: 30rpx;
}

.form-field {
  flex: 1;
  position: relative;
  min-width: 0; /* Allow flex child to shrink below content size */
  margin-bottom: 20rpx; /* Add space for error messages */
}

/* Form fields */
.field-label {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333;
}

.required {
  color: #f56c6c;
  margin-right: 6rpx;
  font-weight: bold;
}

.optional {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
}

.field-input {
  height: 88rpx;
  padding: 0 24rpx;
  border: 1rpx solid #dcdfe6;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #FFFFFF;
  width: 100%;
  box-sizing: border-box;
  transition: all 0.3s;
}

.field-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2rpx rgba(74, 102, 183, 0.2);
}

/* Picker style */
.field-picker {
  height: 88rpx;
  border: 1rpx solid #dcdfe6;
  border-radius: 12rpx;
  position: relative;
  background-color: #FFFFFF;
  width: 100%;
}

.picker-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
}


/* Location picker */
.location-picker {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.location-input {
  flex: 1;
  color: #666;
}

.location-input.has-value {
  color: #333;
}

.location-btn {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  border-radius: 12rpx;
  padding: 0 20rpx;
  cursor: pointer;
}

.location-icon {
  font-size: 28rpx;
  color: #FFFFFF;
  margin-right: 8rpx;
}

.location-text {
  font-size: 28rpx;
  color: #FFFFFF;
}



/* Validation styles */
.has-error .field-input,
.has-error .field-picker,
.has-error .location-picker {
  border-color: #f56c6c;
  background-color: #fff5f5;
}

.error-message {
  position: absolute;
  left: 0;
  top: 100%;
  font-size: 22rpx;
  color: #f56c6c;
  margin-top: 6rpx;
}

/* 权限授权样式 */
.auth-users-container {
  margin-top: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.auth-user-card {
  background: #fff;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

.auth-user-card:hover {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transform: translateY(-1rpx);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.card-info {
  flex: 1;
  min-width: 0;
}

.card-name {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.card-phone {
  display: block;
  font-size: 26rpx;
  color: #666;
}

.card-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-shrink: 0;
}

.unlock-badge {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  background: #f5f5f5;
  color: #999;
}

.unlock-badge.active {
  background: #e6f7ff;
  color: var(--primary-color, #42a5f5);
}

.badge-text {
  font-weight: 500;
}

.icon-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: none;
  background: transparent;
  transition: all 0.2s ease;
  flex-shrink: 0;
  cursor: pointer;
}

.icon-btn .icon {
  font-size: 28rpx;
}

.edit-icon-btn {
  color: var(--primary-color, #42a5f5);
}

.edit-icon-btn:hover {
  background: rgba(66, 165, 245, 0.1);
}

.delete-icon-btn {
  color: #ff4d4f;
}

.delete-icon-btn:hover {
  background: rgba(255, 77, 79, 0.1);
}

.add-user-section {
  margin-top: 24rpx;
}

.add-user-btn {
  width: 100%;
  height: 88rpx;
  background: transparent;
  border: 2rpx dashed #d9d9d9;
  border-radius: 8rpx;
  color: var(--primary-color, #42a5f5);
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.2s ease;
}

.add-user-btn:hover {
  border-color: var(--primary-color, #42a5f5);
  background: rgba(66, 165, 245, 0.04);
}

.btn-icon {
  font-size: 32rpx;
  font-weight: 600;
}

.btn-text {
  font-weight: 500;
}

/* 弹窗样式 */
.user-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.45);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.user-modal {
  background: #fff;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  animation: modalFadeIn 0.2s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.modal-close {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-icon {
  font-size: 24rpx;
  color: #999;
}

.modal-body {
  padding: 32rpx;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.input-item {
  position: relative;
  margin-bottom: 20rpx;
}

.input-label {
  font-size: 28rpx;
  margin-bottom: 8rpx;
  color: #595959;
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.input-field {
  width: 100%;
  height: 80rpx;
  padding: 0 16rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.input-field:focus {
  border-color: var(--primary-color, #42a5f5);
  box-shadow: 0 0 0 2rpx rgba(66, 165, 245, 0.2);
  outline: none;
}

.input-item.has-error .input-field {
  border-color: #ff4d4f;
}

.error-msg {
  position: absolute;
  left: 0;
  top: 100%;
  font-size: 22rpx;
  color: #ff4d4f;
  margin-top: 4rpx;
}

.switch-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
}

.switch-label {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.switch-label text:first-child {
  font-size: 28rpx;
  color: #595959;
  font-weight: 500;
}

.switch-hint {
  font-size: 24rpx;
  color: #8c8c8c;
}

.permission-switch {
  transform: scale(0.9);
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16rpx;
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.btn {
  height: 72rpx;
  line-height: 72rpx;
  padding: 0 32rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary {
  flex: 1;
  background: #f5f5f5;
  color: #666;
}

.btn-secondary:hover {
  background: #e6e6e6;
}

.btn-primary {
  flex: 1;
  background: var(--primary-color, #42a5f5);
  color: #fff;
}

.btn-primary:hover {
  background: #1890ff;
}

.btn-disabled {
  background: #d9d9d9 !important;
  color: #999 !important;
  cursor: not-allowed !important;
}

.btn-disabled:hover {
  background: #d9d9d9 !important;
}

@media screen and (max-width: 768rpx) {
  .card-header {
    flex-wrap: wrap;
    gap: 12rpx;
  }

  .card-actions {
    width: 100%;
    justify-content: flex-end;
    margin-top: 12rpx;
  }

  .user-modal {
    margin: 20rpx;
    max-width: none;
  }

  .modal-body {
    padding: 24rpx;
  }

  .modal-footer {
    padding: 16rpx 24rpx 24rpx;
  }
}
</style>