<template>
  <view class="container">
    <view class="header">
      <view class="back-btn" @click="goBack">
        <uni-icons type="left" size="20" color="#333"></uni-icons>
      </view>
      <view class="title">授权管理</view>
    </view>
    
    <view class="content">
      <!-- 设备信息 -->
      <view class="device-info-card">
        <view class="device-name">{{deviceName}}</view>
        <view v-if="!isGroup" class="device-code">气瓶编码: {{deviceCode}}</view>
        <view v-if="!isGroup" class="device-alarm">报警器编号: {{deviceAlarmCode}}</view>
        <view v-if="isGroup" class="device-group-info">
          <text>设备组 · {{deviceCount}}个设备</text>
        </view>
      </view>
      
      <!-- 授权类型选择 -->
      <view class="auth-type-selector" v-if="isGroup">
        <view 
          class="auth-type-tab" 
          :class="{'active': authType === 'group'}" 
          @click="switchAuthType('group')"
        >
          <text>组授权</text>
        </view>
        <view 
          class="auth-type-tab" 
          :class="{'active': authType === 'device'}" 
          @click="switchAuthType('device')"
        >
          <text>设备授权</text>
        </view>
      </view>
      
      <!-- 设备组授权说明 -->
      <view class="group-auth-info" v-if="isGroup && authType === 'group'">
        <uni-icons type="info" size="16" color="#2979ff"></uni-icons>
        <text>组授权将应用于组内所有设备</text>
      </view>
      
      <!-- 设备选择器（设备组模式下的设备授权） -->
      <view class="device-selector" v-if="isGroup && authType === 'device'">
        <view class="device-selector-header">
          <text class="selector-title">请选择需要授权的设备</text>
        </view>
        
        <view class="device-cards-container">
          <scroll-view scroll-x class="device-cards-scroll" show-scrollbar="false">
            <view class="device-cards">
              <view
                v-for="(device, index) in groupDevices"
                :key="index"
                class="device-card"
                :class="{'active': currentDeviceIndex === index}"
                @click="selectDevice(index)"
              >
                <view class="device-card-icon">
                  <uni-icons :type="currentDeviceIndex === index ? 'checkbox-filled' : 'circle'" size="20" :color="currentDeviceIndex === index ? '#2979ff' : '#999'"></uni-icons>
                </view>
                <view class="device-card-content">
                  <view class="device-card-name">{{device.name}}</view>
                  <view class="device-card-status" :class="{'online': device.isOnline, 'offline': !device.isOnline}">
                    <view class="status-dot"></view>
                    <text>{{device.isOnline ? '在线' : '离线'}}</text>
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
        
        <view class="selected-device-info" v-if="selectedDevice">
          <view class="selected-device-header">
            <view class="header-icon">
              <uni-icons type="info-filled" size="16" color="#2979ff"></uni-icons>
            </view>
            <text class="header-title">设备信息</text>
          </view>
          
          <view class="device-info-grid">
            <view class="info-item">
              <text class="info-label">气瓶编码</text>
              <text class="info-value">{{selectedDevice.code || '未设置'}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">报警器编号</text>
              <text class="info-value">{{selectedDevice.alarmCode || '未设置'}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">购买日期</text>
              <text class="info-value">{{selectedDevice.purchaseDate || '未设置'}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">阀门状态</text>
              <text class="info-value" :class="{'text-success': selectedDevice.valveOpen, 'text-error': !selectedDevice.valveOpen}">
                {{selectedDevice.valveOpen ? '开启' : '关闭'}}
              </text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 授权人员列表 -->
      <view class="section-title">
        <text>授权人员列表</text>
        <view class="title-actions">
          <text>{{currentAuthorizedUsers.length}}人</text>
        </view>
      </view>
      
      <view class="authorized-list" v-if="currentAuthorizedUsers.length > 0">
        <view class="authorized-item" v-for="(user, index) in currentAuthorizedUsers" :key="index">
          <view class="user-info">
            <view class="user-name">{{user.name}}</view>
            <view class="user-phone">{{user.phone}}</view>
          </view>
          <view class="user-auth">
            <view class="auth-tag" v-if="user.canOpenValve">
              <text>可开阀</text>
            </view>
            <view class="auth-tag disabled" v-else>
              <text>不可开阀</text>
            </view>
          </view>
          <view class="user-actions">
            <view class="action-btn edit" @click="editUser(index)">
              <uni-icons type="compose" size="18" color="#2979ff"></uni-icons>
            </view>
            <view class="action-btn delete" @click="confirmDeleteUser(index)">
              <uni-icons type="trash" size="18" color="#FF3B30"></uni-icons>
            </view>
          </view>
        </view>
      </view>
      
      <view class="empty-state" v-else>
        <image src="/static/images/no-data.png" mode="aspectFit"></image>
        <view class="empty-text">暂无授权人员</view>
      </view>
      
      <!-- 添加授权按钮 -->
      <view class="add-btn-container">
        <button class="add-user-btn" @click="showAddUserModal">
          <uni-icons type="plusempty" size="20" color="#FFFFFF"></uni-icons>
          <text>添加授权人员</text>
        </button>
      </view>
    </view>
    
    <!-- 添加/编辑用户弹窗 -->
    <view class="modal" v-if="showUserModal">
      <view class="modal-mask" @click="cancelUserModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">{{isEditing ? '编辑授权人员' : '添加授权人员'}}</text>
          <view class="modal-close" @click="cancelUserModal">
            <uni-icons type="closeempty" size="24" color="#666"></uni-icons>
          </view>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">姓名</text>
            <input class="form-input" :class="{'form-input-error': formErrors.name}" v-model="currentUser.name" placeholder="请输入姓名" />
            <view class="form-error" v-if="formErrors.name">{{formErrors.name}}</view>
          </view>
          <view class="form-item">
            <text class="form-label">手机号码</text>
            <input class="form-input" :class="{'form-input-error': formErrors.phone}" v-model="currentUser.phone" placeholder="请输入手机号码" type="number" maxlength="11" />
            <view class="form-error" v-if="formErrors.phone">{{formErrors.phone}}</view>
          </view>
          <view class="form-item switch-item">
            <text class="form-label">燃气开阀授权</text>
            <switch :checked="currentUser.canOpenValve" @change="switchChange" color="#2979ff" />
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="modal-btn secondary" @click="cancelUserModal">取消</button>
          <button class="modal-btn primary" @click="saveUser">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      deviceId: null,
      deviceName: '',
      deviceCode: '',
      deviceAlarmCode: '',
      isGroup: false,
      deviceCount: 0,
      groupDevices: [],
      currentDeviceIndex: 0,
      selectedDevice: {
        id: null,
        name: '',
        code: '',
        alarmCode: ''
      },
      authType: 'group', // 'group' 或 'device'
      authorizedUsers: {
        group: [], // 组授权
        devices: {} // 设备授权，键为设备ID，值为授权用户数组
      },
      showUserModal: false,
      isEditing: false,
      editingIndex: -1,
      currentUser: {
        name: '',
        phone: '',
        canOpenValve: true
      },
      formErrors: {
        name: '',
        phone: ''
      }
    }
  },
  
  computed: {
    // 当前显示的授权用户列表
    currentAuthorizedUsers() {
      if (!this.isGroup || this.authType === 'group') {
        return this.authorizedUsers.group
      } else {
        // 设备授权模式
        const deviceId = this.selectedDevice.id
        if (!deviceId) return []
        return this.authorizedUsers.devices[deviceId] || []
      }
    }
  },
  
  onLoad(options) {
    // 获取设备ID和其他信息
    if (options.id) {
      this.deviceId = options.id
      this.loadDeviceInfo()
    }
  },
  
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 加载设备信息
    loadDeviceInfo() {
      // 实际应用中应从API获取设备信息
      
      // 从上一页面获取设备信息
      const eventChannel = this.getOpenerEventChannel()
      eventChannel.on('deviceInfo', (data) => {
        console.log('Received device info:', data)
        this.deviceName = data.name || '未命名设备'
        this.deviceCode = data.code || ''
        this.deviceAlarmCode = data.alarmCode || ''
        this.isGroup = data.isGroup || false
        
        // 如果是设备组，加载组内设备
        if (this.isGroup && data.devices) {
          this.groupDevices = data.devices
          this.deviceCount = data.deviceCount || this.groupDevices.length
          if (this.groupDevices.length > 0) {
            this.selectedDevice = this.groupDevices[0]
          }
          
          // 初始化设备授权数据结构
          this.groupDevices.forEach(device => {
            if (device.id) {
              this.authorizedUsers.devices[device.id] = []
            }
          })
        }
        
        // 加载授权用户
        this.loadAuthorizedUsers()
      })
    },
    
    // 加载授权用户列表
    loadAuthorizedUsers() {
      // 实际应用中应从API获取授权用户列表
      // 这里使用模拟数据
      
      // 组授权数据
      this.authorizedUsers.group = [
        {
          name: '张三',
          phone: '13812345678',
          canOpenValve: true
        },
        {
          name: '李四',
          phone: '13987654321',
          canOpenValve: false
        }
      ]
      
      // 如果是设备组，为每个设备添加授权数据
      if (this.isGroup) {
        this.groupDevices.forEach((device, index) => {
          if (device.id) {
            // 为每个设备创建单独的授权用户列表
            this.authorizedUsers.devices[device.id] = [
              {
                name: '王五',
                phone: '13566778899',
                canOpenValve: true
              }
            ]
          }
        })
      }
    },
    
    // 切换授权类型（组/设备）
    switchAuthType(type) {
      this.authType = type
    },
    
    // 设备选择器变更
    onDeviceChange(e) {
      const index = e.detail.value
      this.currentDeviceIndex = index
      this.selectedDevice = this.groupDevices[index]
    },
    
    // 直接点击选择设备
    selectDevice(index) {
      this.currentDeviceIndex = index
      this.selectedDevice = this.groupDevices[index]
    },
    
    // 显示添加用户弹窗
    showAddUserModal() {
      this.isEditing = false
      this.currentUser = {
        name: '',
        phone: '',
        canOpenValve: true
      }
      this.formErrors = {
        name: '',
        phone: ''
      }
      this.showUserModal = true
    },
    
    // 编辑用户
    editUser(index) {
      this.isEditing = true
      this.editingIndex = index
      // 复制一份用户数据，避免直接修改列表数据
      this.currentUser = JSON.parse(JSON.stringify(this.currentAuthorizedUsers[index]))
      this.formErrors = {
        name: '',
        phone: ''
      }
      this.showUserModal = true
    },
    
    // 确认删除用户
    confirmDeleteUser(index) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除授权人员 "${this.currentAuthorizedUsers[index].name}" 吗？`,
        success: (res) => {
          if (res.confirm) {
            this.deleteUser(index)
          }
        }
      })
    },
    
    // 删除用户
    deleteUser(index) {
      // 实际应用中应调用API删除
      if (!this.isGroup || this.authType === 'group') {
        this.authorizedUsers.group.splice(index, 1)
      } else {
        const deviceId = this.selectedDevice.id
        if (deviceId && this.authorizedUsers.devices[deviceId]) {
          this.authorizedUsers.devices[deviceId].splice(index, 1)
        }
      }
      
      uni.showToast({
        title: '删除成功',
        icon: 'success'
      })
    },
    
    // 取消用户弹窗
    cancelUserModal() {
      this.showUserModal = false
      this.formErrors = {
        name: '',
        phone: ''
      }
    },
    
    // 保存用户
    saveUser() {
      // 重置错误信息
      this.formErrors = {
        name: '',
        phone: ''
      }
      
      let isValid = true
      
      // 表单验证
      if (!this.currentUser.name) {
        this.formErrors.name = '请输入姓名'
        isValid = false
      }
      
      if (!this.currentUser.phone) {
        this.formErrors.phone = '请输入手机号码'
        isValid = false
      } else if (!/^1\d{10}$/.test(this.currentUser.phone)) {
        this.formErrors.phone = '请输入正确的手机号码'
        isValid = false
      }
      
      // 如果表单验证不通过，则不继续处理
      if (!isValid) {
        return
      }
      
      // 保存用户数据
      const userData = JSON.parse(JSON.stringify(this.currentUser))
      
      if (!this.isGroup || this.authType === 'group') {
        // 组授权模式
        if (this.isEditing) {
          this.authorizedUsers.group.splice(this.editingIndex, 1, userData)
        } else {
          this.authorizedUsers.group.push(userData)
        }
      } else {
        // 设备授权模式
        const deviceId = this.selectedDevice.id
        if (deviceId) {
          if (!this.authorizedUsers.devices[deviceId]) {
            this.authorizedUsers.devices[deviceId] = []
          }
          
          if (this.isEditing) {
            this.authorizedUsers.devices[deviceId].splice(this.editingIndex, 1, userData)
          } else {
            this.authorizedUsers.devices[deviceId].push(userData)
          }
        }
      }
      
      uni.showToast({
        title: this.isEditing ? '更新成功' : '添加成功',
        icon: 'success'
      })
      
      this.showUserModal = false
    },
    
    // 开关状态变化
    switchChange(e) {
      this.currentUser.canOpenValve = e.detail.value
    }
  }
}
</script>

<style scoped>
.container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.header {
  position: relative;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  display: flex;
  align-items: center;
}

.back-btn {
  position: absolute;
  left: 30rpx;
  height: 60rpx;
  width: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: bold;
}

.content {
  padding: 30rpx;
}

.device-info-card {
  background-color: #fff;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.device-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.device-code, .device-alarm {
  font-size: 26rpx;
  color: #666;
  margin-top: 10rpx;
}

.device-group-info {
  font-size: 26rpx;
  color: #2979ff;
  background-color: rgba(41,121,255,0.1);
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  display: inline-block;
  margin-top: 10rpx;
}

.auth-type-selector {
  display: flex;
  background-color: #fff;
  border-radius: 15rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  margin-bottom: 30rpx;
  overflow: hidden;
}

.auth-type-tab {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.auth-type-tab.active {
  color: #2979ff;
  font-weight: 500;
}

.auth-type-tab.active::after {
  content: '';
  position: absolute;
  left: 25%;
  bottom: 0;
  width: 50%;
  height: 4rpx;
  background-color: #2979ff;
  border-radius: 2rpx;
}

.group-auth-info {
  background-color: rgba(41,121,255,0.1);
  color: #2979ff;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 26rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
}

.group-auth-info uni-icons {
  margin-right: 10rpx;
  flex-shrink: 0;
}

.device-selector {
  background-color: #fff;
  border-radius: 15rpx;
  padding: 25rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  margin-bottom: 30rpx;
}

.device-selector-header {
  margin-bottom: 20rpx;
}

.selector-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.device-cards-container {
  margin: 0 -25rpx;
  position: relative;
}

.device-cards-scroll {
  white-space: nowrap;
}

.device-cards {
  display: inline-flex;
  padding: 0 25rpx;
}

.device-card {
  display: flex;
  align-items: center;
  background-color: #f9f9f9;
  padding: 15rpx 30rpx;
  border-radius: 50rpx;
  margin-right: 15rpx;
  border: 1px solid #eeeeee;
  transition: all 0.2s ease;
}

.device-card.active {
  background-color: rgba(41, 121, 255, 0.08);
  border-color: rgba(41, 121, 255, 0.3);
}

.device-card-icon {
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.device-card-content {
  display: flex;
  flex-direction: column;
}

.device-card-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.device-card-status {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  margin-top: 5rpx;
}

.device-card-status .status-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  margin-right: 5rpx;
}

.device-card-status.online {
  color: #4CD964;
}

.device-card-status.online .status-dot {
  background-color: #4CD964;
}

.device-card-status.offline {
  color: #999;
}

.device-card-status.offline .status-dot {
  background-color: #999;
}

.selected-device-info {
  margin-top: 25rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
}

.selected-device-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.header-icon {
  margin-right: 10rpx;
  display: flex;
  align-items: center;
}

.header-title {
  font-size: 26rpx;
  color: #2979ff;
  font-weight: 500;
}

.device-info-grid {
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  width: 50%;
  margin-bottom: 15rpx;
}

.info-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
  display: block;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.text-success {
  color: #4CD964;
}

.text-error {
  color: #FF3B30;
}

.picker-view {
  height: 80rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  background-color: #fafafa;
  display: none;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.selected-device-info {
  margin-top: 20rpx;
  padding: 15rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.info-row {
  display: flex;
  padding: 5rpx 0;
  font-size: 24rpx;
}

.info-label {
  color: #666;
  margin-right: 10rpx;
}

.info-value {
  color: #333;
  font-weight: 500;
}

.section-title {
  margin: 30rpx 0 20rpx;
  font-size: 30rpx;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-actions {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

.authorized-list {
  background-color: #fff;
  border-radius: 15rpx;
  padding: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.authorized-item {
  display: flex;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  align-items: center;
}

.authorized-item:last-child {
  border-bottom: none;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 28rpx;
  margin-bottom: 5rpx;
}

.user-phone {
  font-size: 24rpx;
  color: #666;
}

.user-auth {
  display: flex;
  margin-right: 20rpx;
}

.auth-tag {
  background-color: rgba(76, 217, 100, 0.1);
  border: 1px solid rgba(76, 217, 100, 0.3);
  color: #4CD964;
  font-size: 22rpx;
  padding: 4rpx 15rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
}

.auth-tag uni-icons {
  margin-right: 5rpx;
}

.auth-tag.disabled {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  color: #999;
}

.user-actions {
  display: flex;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
  background-color: #f5f5f5;
}

.action-btn.edit {
  background-color: rgba(41, 121, 255, 0.1);
}

.action-btn.delete {
  background-color: rgba(255, 59, 48, 0.1);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 0;
  background-color: #fff;
  border-radius: 15rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.add-btn-container {
  margin-top: 50rpx;
  display: flex;
  justify-content: center;
}

.add-user-btn {
  background: linear-gradient(135deg, #2979ff, #1565c0);
  color: #fff;
  border-radius: 40rpx;
  padding: 0 60rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(41,121,255,0.3);
}

.add-user-btn uni-icons {
  margin-right: 10rpx;
}

/* 模态弹窗 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  width: 80%;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
  z-index: 1001;
}

.modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 34rpx;
  font-weight: bold;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
}

.modal-footer {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.modal-btn.secondary {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 20rpx;
}

.modal-btn.primary {
  background: linear-gradient(135deg, #2979ff, #1565c0);
  color: #fff;
}

/* 表单样式 */
.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  display: block;
}

.form-input {
  height: 80rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.form-input-error {
  border-color: #FF3B30;
  background-color: rgba(255, 59, 48, 0.05);
}

.form-error {
  font-size: 24rpx;
  color: #FF3B30;
  margin-top: 8rpx;
  padding-left: 10rpx;
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-item .form-label {
  margin-bottom: 0;
}
</style> 