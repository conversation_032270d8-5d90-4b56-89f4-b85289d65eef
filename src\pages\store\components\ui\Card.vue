<template>
  <view class="card">
    <view class="card-header" v-if="title">
      <text class="card-title">{{ title }}</text>
    </view>
    <view class="card-body">
      <slot></slot>
    </view>
  </view>
</template>

<script setup>
// 定义props
const props = defineProps({
  title: {
    type: String,
    default: ''
  }
})
</script>

<style scoped>
.card {
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 24rpx;
}

.card-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: var(--primary-color);
  border-radius: 4rpx;
}

.card-body {
  padding: 30rpx;
}
</style> 