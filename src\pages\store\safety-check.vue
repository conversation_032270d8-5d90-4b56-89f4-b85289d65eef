<template>
  <view class="container" :class="{ 'has-bottom-button': showBackButton }">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 多条安全核查列表 -->
    <view v-else-if="safetyCheckList.length > 1 && !showBackButton" class="content">
      <!-- 测试按钮 -->
      <view class="test-buttons">
        <button class="test-btn" @click="toggleTestMode">切换到单条记录测试</button>
        <text class="test-info">当前显示：多条记录列表</text>
      </view>

      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>安全核查记录</text>
        </view>
        <view class="check-list">
          <view
            v-for="(item, index) in safetyCheckList"
            :key="index"
            class="check-item"
            @click="selectSafetyCheck(item)"
          >
            <view class="check-item-header">
              <text class="check-item-title">核查记录 {{ index + 1 }}</text>
              <text class="check-item-time">{{ item.serviceProvider.checkTime }}</text>
            </view>
            <view class="check-item-info">
              <text class="check-item-provider">{{ item.serviceProvider.name }}</text>
              <text class="check-item-contact">{{ item.serviceProvider.contact }}</text>
            </view>
            <view class="check-item-arrow">
              <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 单条安全核查详情 -->
    <view v-else class="content">
      <!-- 测试按钮（当只有一条记录时显示） -->
      <view v-if="!showBackButton && safetyCheckList.length === 1" class="test-buttons">
        <button class="test-btn" @click="toggleTestMode">切换到多条记录测试</button>
        <text class="test-info">当前显示：单条记录详情</text>
      </view>


      <!-- 服务商信息 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>服务商信息</text>
        </view>
        <view class="info-card">
          <view class="info-row">
            <text class="info-label">服务商名称</text>
            <text class="info-value">{{ safetyCheckData.serviceProvider.name }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">联系人</text>
            <text class="info-value">{{ safetyCheckData.serviceProvider.contact }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">联系电话</text>
            <text class="info-value">{{ safetyCheckData.serviceProvider.phone }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">核查时间</text>
            <text class="info-value">{{ safetyCheckData.serviceProvider.checkTime }}</text>
          </view>
        </view>
      </view>

      <!-- 使用场景 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>使用场景</text>
        </view>
        <view class="image-section">
          <text class="scene-desc">{{ safetyCheckData.usageScene.description }}</text>
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.usageScene.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.usageScene.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 气瓶存放区 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>气瓶存放区</text>
        </view>
        <view class="image-section">
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.cylinderStorage.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.cylinderStorage.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 报警器安装 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>报警器安装</text>
        </view>
        <view class="image-section">
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.alarmInstallation.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.alarmInstallation.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 消防设备 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>消防设备</text>
        </view>
        <view class="image-section">
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.fireEquipment.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.fireEquipment.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 管道阀门安装图 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>管道阀门安装图</text>
        </view>
        <view class="image-section">
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.pipelineValve.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.pipelineValve.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 燃气设备使用图 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>燃气设备使用图</text>
        </view>
        <view class="image-section">
          <text class="scene-desc">{{ safetyCheckData.gasEquipment.description }}</text>
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.gasEquipment.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.gasEquipment.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部固定返回按钮（当从列表进入详情时显示） -->
    <view v-if="showBackButton" class="actions">
      <button class="action-btn uni-button uni-button-primary" @click="backToList">
        <text class="back-text">返回列表</text>
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const storeId = ref(null)
const loading = ref(true)
const safetyCheckData = ref({})
const safetyCheckList = ref([])
const showBackButton = ref(false)

// 页面加载时的处理
const onLoad = (options) => {
  storeId.value = options.id;
  loadSafetyCheckData();
}

// 加载安全核查数据
const loadSafetyCheckData = () => {
  loading.value = true;

  // 模拟API调用
  setTimeout(() => {
    // 模拟多条安全核查数据
    const mockData = [
      {
        serviceProvider: {
          name: "广州市燃气安全检测有限公司",
          contact: "李工程师",
          phone: "020-12345678",
          checkTime: "2023-10-15 14:30"
        },
        usageScene: {
          description: "通风良好，有通风设备",
          images: [
            "https://via.placeholder.com/300x200/4a66b7/ffffff?text=通风设备1",
            "https://via.placeholder.com/300x200/2979ff/ffffff?text=通风设备2"
          ]
        },
        cylinderStorage: {
          images: [
            "https://via.placeholder.com/300x200/34c759/ffffff?text=气瓶存放1",
            "https://via.placeholder.com/300x200/ff9500/ffffff?text=气瓶存放2",
            "https://via.placeholder.com/300x200/ff3b30/ffffff?text=气瓶存放3"
          ]
        },
        alarmInstallation: {
          images: [
            "https://via.placeholder.com/300x200/909399/ffffff?text=报警器1",
            "https://via.placeholder.com/300x200/606266/ffffff?text=报警器2"
          ]
        },
        fireEquipment: {
          images: [
            "https://via.placeholder.com/300x200/e6a23c/ffffff?text=消防设备1",
            "https://via.placeholder.com/300x200/f56c6c/ffffff?text=消防设备2",
            "https://via.placeholder.com/300x200/67c23a/ffffff?text=消防设备3"
          ]
        },
        pipelineValve: {
          images: [
            "https://via.placeholder.com/300x200/409eff/ffffff?text=管道阀门1",
            "https://via.placeholder.com/300x200/303133/ffffff?text=管道阀门2"
          ]
        },
        gasEquipment: {
          description: "灶具有息火保护装置，热水器烟道是强排",
          images: [
            "https://via.placeholder.com/300x200/909399/ffffff?text=燃气灶具",
            "https://via.placeholder.com/300x200/606266/ffffff?text=热水器",
            "https://via.placeholder.com/300x200/303133/ffffff?text=烟道设备"
          ]
        }
      },
      {
        serviceProvider: {
          name: "深圳市安全检测中心",
          contact: "王工程师",
          phone: "0755-87654321",
          checkTime: "2023-09-20 10:15"
        },
        usageScene: {
          description: "室内通风一般，建议增加通风设备",
          images: [
            "https://via.placeholder.com/300x200/ff6b6b/ffffff?text=通风设备A",
            "https://via.placeholder.com/300x200/4ecdc4/ffffff?text=通风设备B"
          ]
        },
        cylinderStorage: {
          images: [
            "https://via.placeholder.com/300x200/45b7d1/ffffff?text=气瓶存放A",
            "https://via.placeholder.com/300x200/f9ca24/ffffff?text=气瓶存放B"
          ]
        },
        alarmInstallation: {
          images: [
            "https://via.placeholder.com/300x200/6c5ce7/ffffff?text=报警器A",
            "https://via.placeholder.com/300x200/a55eea/ffffff?text=报警器B"
          ]
        },
        fireEquipment: {
          images: [
            "https://via.placeholder.com/300x200/26de81/ffffff?text=消防设备A",
            "https://via.placeholder.com/300x200/fd79a8/ffffff?text=消防设备B"
          ]
        },
        pipelineValve: {
          images: [
            "https://via.placeholder.com/300x200/778ca3/ffffff?text=管道阀门A",
            "https://via.placeholder.com/300x200/2d3436/ffffff?text=管道阀门B"
          ]
        },
        gasEquipment: {
          description: "燃气设备符合安全标准，定期维护良好",
          images: [
            "https://via.placeholder.com/300x200/00b894/ffffff?text=燃气灶具A",
            "https://via.placeholder.com/300x200/e17055/ffffff?text=热水器A"
          ]
        }
      }
    ];

    // 根据storeId或其他条件决定返回单条还是多条数据
    // 这里为了演示，我们默认返回多条数据来测试功能
    // 在实际项目中，这里应该根据API返回的数据来决定
    const shouldReturnMultiple = true; // 改为true来测试多条记录功能

    if (shouldReturnMultiple) {
      safetyCheckList.value = mockData;
    } else {
      safetyCheckList.value = [mockData[0]];
      safetyCheckData.value = mockData[0];
    }

    loading.value = false;
  }, 1000);
}

// 选择安全核查记录
const selectSafetyCheck = (checkData) => {
  safetyCheckData.value = checkData;
  showBackButton.value = true;
}

// 返回列表
const backToList = () => {
  showBackButton.value = false;
  safetyCheckData.value = {};
}

// 切换测试模式
const toggleTestMode = () => {
  loading.value = true;
  showBackButton.value = false;

  setTimeout(() => {
    if (safetyCheckList.value.length > 1) {
      // 当前是多条记录，切换到单条
      safetyCheckList.value = [safetyCheckList.value[0]];
      safetyCheckData.value = safetyCheckList.value[0];
    } else {
      // 当前是单条记录，重新加载多条数据
      loadSafetyCheckData();
      return;
    }
    loading.value = false;
  }, 500);
}

// 预览图片
const previewImage = (currentImage, imageList) => {
  uni.previewImage({
    urls: imageList,
    current: currentImage
  });
}

// 直接加载数据
loadSafetyCheckData();

// 导出页面生命周期方法供uni-app使用
defineExpose({
  onLoad
})
</script>

<style scoped>
:root {
  --primary-color: #4a66b7;
}

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(74, 102, 183, 0.1);
  border-top: 6rpx solid #4a66b7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 30rpx;
  color: #4a66b7;
  font-size: 32rpx;
  font-weight: 500;
}

/* 内容区域 */
.content {
  padding: 40rpx 24rpx 60rpx;
  max-width: 750rpx;
  margin: 0 auto;
}

/* 当有底部固定按钮时，增加底部内边距 */
.has-bottom-button .content {
  /* 按钮高度88rpx + 容器内边距60rpx + 额外间距40rpx = 188rpx */
  padding-bottom: calc(188rpx + env(safe-area-inset-bottom));
  padding-bottom: calc(188rpx + constant(safe-area-inset-bottom)); /* iOS 11.0-11.2 兼容 */
}

.section {
  margin-bottom: 48rpx;
  position: relative;
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 0 8rpx;
  position: relative;
}

.section-title-bar {
  width: 8rpx;
  height: 36rpx;
  background: linear-gradient(135deg, #4a66b7, #667eea);
  margin-right: 20rpx;
  border-radius: 4rpx;
  box-shadow: 0 2rpx 8rpx rgba(74, 102, 183, 0.3);
}

.section-title text {
  font-size: 36rpx;
  font-weight: 700;
  color: #2c3e50;
  letter-spacing: 1rpx;
}

/* 测试按钮 */
.test-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.test-info {
  font-size: 24rpx;
  color: #5a6c7d;
  background: rgba(74, 102, 183, 0.1);
  padding: 12rpx 24rpx;
  border-radius: 12rpx;
}

.test-btn {
  background: linear-gradient(135deg, #4a66b7, #667eea);
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(74, 102, 183, 0.3);
}

.test-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(74, 102, 183, 0.4);
}

/* 底部固定按钮区域 - 参考 add-store 页面样式 */
.actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 30rpx 30rpx;
  /* 兼容安卓和iOS的安全区域 */
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  padding-bottom: calc(30rpx + constant(safe-area-inset-bottom)); /* iOS 11.0-11.2 兼容 */
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20rpx;
  z-index: 1000;
}

.action-btn {
  flex: 1;
  width: 100%;
  border-radius: 44rpx;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

/* UniButton 样式 - 与 add-store 保持一致 */
.uni-button {
  min-width: 200rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  border: none;
  overflow: hidden;
}

.uni-button::after {
  border: none;
}

.uni-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.uni-button-primary {
  color: #fff;
  background: linear-gradient(135deg, #42b0ff, var(--primary-color));
}

.back-text {
  font-size: 28rpx;
  color: white;
  font-weight: 500;
  line-height: 1;
}

/* 安全核查列表 */
.check-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.check-item {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 24rpx;
  padding: 36rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.check-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.check-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.check-item-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #2c3e50;
}

.check-item-time {
  font-size: 26rpx;
  color: #5a6c7d;
  font-weight: 500;
}

.check-item-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.check-item-provider {
  font-size: 28rpx;
  color: #4a66b7;
  font-weight: 600;
}

.check-item-contact {
  font-size: 26rpx;
  color: #5a6c7d;
}

.check-item-arrow {
  position: absolute;
  right: 36rpx;
  top: 50%;
  transform: translateY(-50%);
}



/* 信息卡片 */
.info-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 24rpx;
  padding: 48rpx 36rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.info-row:hover {
  background-color: rgba(74, 102, 183, 0.02);
  border-radius: 12rpx;
  margin: 0 -12rpx;
  padding: 24rpx 12rpx;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  width: 240rpx;
  font-size: 28rpx;
  color: #5a6c7d;
  font-weight: 500;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  text-align: right;
  font-size: 30rpx;
  color: #2c3e50;
  font-weight: 600;
  word-break: break-all;
}

/* 图片区域 */
.image-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 24rpx;
  padding: 48rpx 36rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
}

.scene-desc {
  display: block;
  font-size: 30rpx;
  color: #5a6c7d;
  margin-bottom: 36rpx;
  line-height: 1.7;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  padding: 28rpx 24rpx;
  border-radius: 16rpx;
  border-left: 6rpx solid #4a66b7;
  font-weight: 500;
  box-shadow: 0 4rpx 16rpx rgba(74, 102, 183, 0.1);
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.image-item {
  height: 260rpx;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.image-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.image-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(74, 102, 183, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.image-item:active::before {
  opacity: 1;
}

.scene-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  object-fit: cover;
}

/* 响应式布局 */
@media (max-width: 600rpx) {
  .content {
    padding: 32rpx 20rpx 60rpx;
  }

  /* 小屏幕下有底部按钮时的内边距 */
  .has-bottom-button .content {
    /* 小屏幕下按钮高度80rpx + 容器内边距48rpx + 额外间距32rpx = 160rpx */
    padding-bottom: calc(160rpx + env(safe-area-inset-bottom));
    padding-bottom: calc(160rpx + constant(safe-area-inset-bottom)); /* iOS 11.0-11.2 兼容 */
  }

  .section {
    margin-bottom: 40rpx;
  }

  .info-card, .image-section {
    padding: 36rpx 28rpx;
  }

  .image-grid {
    gap: 20rpx;
  }

  .image-item {
    height: 240rpx;
  }

  .actions {
    padding: 24rpx 20rpx;
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
    padding-bottom: calc(24rpx + constant(safe-area-inset-bottom)); /* iOS 11.0-11.2 兼容 */
  }

  .uni-button {
    height: 80rpx;
    line-height: 80rpx;
    font-size: 26rpx;
  }

  .back-icon {
    font-size: 28rpx;
  }

  .back-text {
    font-size: 26rpx;
  }
}
</style>
