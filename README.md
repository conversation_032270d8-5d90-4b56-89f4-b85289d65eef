# 燃气小程序

一款基于uni-app开发的燃气服务微信小程序，提供燃气订购、商城购物、设备监控等功能。

## 功能特点

- **订气服务**：线上订购燃气，支持多种规格选择
- **智能控制**：远程监控与控制气瓶设备
- **商城购物**：购买燃气相关商品
- **租户管理**：多租户管理与审核
- **安全中心**：应急指南与一键报警功能

## 页面结构

### 主Tab页面（5个）

| 名称 | 路径 | 说明 |
|------|------|------|
| 订气 | /pages/index/index | 首页，订气功能入口 |
| 商城 | /pages/mall/index | 燃气相关商品购买 |
| 租户 | /pages/store/index | 租户及资质管理 |
| 智能控制 | /pages/device/index | 气瓶设备监控与控制 |
| 我的 | /pages/user/index | 个人中心 |

### 子页面

- **订气模块**：规格选择、配送信息、支付等
- **商城模块**：商品分类、列表、详情、购物车
- **智能控制**：设备列表、设备详情、数据监控
- **应急中心**：一键报警、应急指南、安全自检
- **用户中心**：台帐管理、历史订单、设置

## 项目启动

### 开发环境

- HBuilderX
- 微信开发者工具

### 启动步骤

1. 使用HBuilderX打开项目
2. 运行到微信开发者工具
3. 微信开发者工具中配置小程序AppID

## 技术栈

- uni-app
- Vue.js
- 微信小程序API

## 注意事项

- 本项目需要在微信小程序管理后台配置位置、地址等相关权限
- 商城、订单等功能需要对接后端API，目前使用的是模拟数据
- 智能控制相关功能需要设备支持远程控制API 