# 安全确认页面功能说明

## 新增功能

### 1. 音频默认不播放
- 音频播放器默认处于暂停状态
- 用户需要手动点击播放按钮才能播放安全事项音频
- 设置了 `autoplay: false` 确保音频不会自动播放

### 2. 设备异常检测与图片上传
当检测到以下任一异常情况时，会显示图片上传区域：
- 气瓶编号状态不为"正常"
- 报警器号状态不为"正常"  
- 瓶阀电量低于20%

#### 异常状态显示
- 异常状态标签会显示为红色警告样式
- 正常状态标签显示为绿色样式

#### 图片上传功能
- 使用 `ImageUploader.vue` 组件
- 支持多图片上传，最多3张
- 提供删除功能
- 在设备异常时，如果未上传图片，无法完成开锁操作

### 3. 组件位置
- `ImageUploader.vue` 组件放置在设备信息卡片和安全事项卡片之间
- 只有在检测到设备异常时才显示
- 采用红色边框和警告色调，突出异常状态

## 测试方法

### 测试正常状态
保持当前设置：
```javascript
deviceInfo: {
  codeStatus: "正常",
  alarmCodeStatus: "正常", 
  battery: 50
}
```

### 测试异常状态
修改以下任一项：
```javascript
deviceInfo: {
  codeStatus: "异常",        // 或
  alarmCodeStatus: "异常",   // 或
  battery: 15               // 低于20
}
```

## 使用说明

1. **正常情况**：用户确认安全事项后可直接开锁
2. **异常情况**：
   - 页面会显示异常上传区域
   - 用户必须上传至少1张现场照片
   - 确认安全事项后才能开锁
   - 开锁成功提示会包含异常处理信息

## 技术实现

- 使用计算属性 `hasDeviceException` 判断设备状态
- 动态显示/隐藏图片上传组件
- 在确认开锁时验证异常情况下的图片上传
- 集成现有的 `ImageUploader.vue` 组件，保持UI一致性
