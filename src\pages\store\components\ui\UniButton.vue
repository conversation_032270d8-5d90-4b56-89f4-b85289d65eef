<template>
  <button 
    class="uni-button" 
    :class="[`uni-button-${type}`, { disabled }]" 
    :disabled="disabled"
    @click="$emit('click')"
  >
    <slot></slot>
  </button>
</template>

<script setup>
// 定义props
const props = defineProps({
  type: {
    type: String,
    default: 'default',
    validator: value => ['default', 'primary', 'success', 'warning', 'danger'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

// 定义emits
const emit = defineEmits(['click'])
</script>

<style scoped>
.uni-button {
  min-width: 200rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  border: none;
  overflow: hidden;
}

.uni-button::after {
  border: none;
}

.uni-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.uni-button-default {
  color: #666;
  background-color: #f5f5f5;
}

.uni-button-primary {
  color: #fff;
  background: linear-gradient(135deg, #42b0ff, var(--primary-color));
}

.uni-button-success {
  color: #fff;
  background: linear-gradient(135deg, #34c759, #2bb34d);
}

.uni-button-warning {
  color: #fff;
  background: linear-gradient(135deg, #ff9500, #ff8000);
}

.uni-button-danger {
  color: #fff;
  background: linear-gradient(135deg, #ff3b30, #e30000);
}

.uni-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.uni-button.disabled:active {
  transform: none;
}
</style> 