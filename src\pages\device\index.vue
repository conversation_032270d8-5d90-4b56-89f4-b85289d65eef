<template>
  <view class="container device-container">
    <view class="header">
      <view class="title">我的设备</view>
      <!-- <view class="add-device" @click="showActionSheet">
        <uniIcons type="plus" size="20" color="#FFFFFF"></uniIcons>
      </view> -->
    </view>
    
    <view class="no-device" v-if="deviceList.length === 0">
      <image src="/static/images/no-device.png" mode="aspectFit"></image>
      <view class="no-device-text">暂无绑定设备</view>
      <button class="add-device-btn" @click="addDevice">添加设备</button>
    </view>
    
    <view class="fullscreen-blocks" v-else>
      <swiper 
        class="fullscreen-swiper" 
        :indicator-dots="false"
        :autoplay="false"
        @change="handleSwiperChange"
      >
        <swiper-item v-for="(device, index) in deviceList" :key="index">
          <view class="device-block" :class="{'device-group': device.isGroup}">
            <view class="block-header">
              <view class="device-name">{{device.name}}</view>
              <view class="device-status" :class="{'status-online': device.isOnline, 'status-offline': !device.isOnline}">
                <view class="status-dot"></view>
                {{device.isOnline ? '在线' : '离线'}}
              </view>
            </view>
            
            <view class="block-content">
              <!-- Device info section -->
              <view class="device-info" v-if="!device.isGroup">
                <view class="info-item">
                  <view class="info-label"><uniIcons type="locked" size="16" color="#666"></uniIcons> 气瓶编码</view>
                  <view class="info-value">{{device.code}} <text v-if="device.codeStatus" class="status-tag normal">{{device.codeStatus}}</text></view>
                </view>
                <view class="info-item">
                  <view class="info-label"><uniIcons type="sound" size="16" color="#666"></uniIcons> 报警器编号</view>
                  <view class="info-value">{{device.alarmCode || '未设置'}} <text v-if="device.alarmCodeStatus" class="status-tag normal">{{device.alarmCodeStatus}}</text></view>
                </view>
                <view class="info-item">
                  <view class="info-label"><uniIcons type="staff" size="16" color="#666"></uniIcons> 充装单位</view>
                  <view class="info-value">{{device.fillingCompany || '未设置'}}</view>
                </view>
                <view class="info-item">
                  <view class="info-label"><uniIcons type="calendar" size="16" color="#666"></uniIcons> 充装时间</view>
                  <view class="info-value">{{device.fillingDate || '未设置'}}</view>
                </view>
                <view class="info-item">
                  <view class="info-label"><uniIcons type="shop" size="16" color="#666"></uniIcons> 充装重量</view>
                  <view class="info-value">{{device.fillingWeight || '未设置'}}</view>
                </view>
                <view class="info-item">
                  <view class="info-label"><uniIcons type="sound" size="16" color="#666"></uniIcons> 报警器</view>
                  <view class="info-value" :class="{'alarm-normal': device.alarmStatus === 'normal', 'alarm-warning': device.alarmStatus === 'warning'}">
                    {{device.alarmStatus === 'normal' ? '正常' : '异常'}}
                  </view>
                </view>
                <view class="info-item">
                  <view class="info-label"><uniIcons type="battery" size="16" color="#666"></uniIcons> 瓶阀电量</view>
                  <view class="info-value">
                    <view class="battery-bar">
                      <view class="battery-fill" :style="{width: device.battery + '%', backgroundColor: getBatteryColor(device.battery)}"></view>
                    </view>
                    <text class="battery-text" :class="{'battery-low': device.battery < 20}" :style="{color: getBatteryTextColor(device.battery)}">{{device.battery}}%</text>
                  </view>
                </view>
              </view>
              
              <!-- Group info section -->
              <view class="device-group-info" v-else>
                <!-- <view class="group-info-header">
                  <view class="group-info">
                    <text class="count">{{device.deviceCount}}</text>
                    <text>个设备</text>
                  </view>
                </view> -->
                <view class="group-devices">
                  <view 
                    class="group-device-item" 
                    v-for="(subDevice, subIndex) in device.devices.slice(0, 3)" 
                    :key="subIndex"
                    @click.stop="showDeviceDetail(subDevice)"
                    :class="{
                      'high-battery': subDevice.battery >= 60,
                      'medium-battery': subDevice.battery < 60 && subDevice.battery >= 30,
                      'low-battery': subDevice.battery < 30
                    }"
                  >
                    <view class="sub-device-info">
                      <text>{{subDevice.name}}</text>
                      <view class="sub-status-dot" :class="{'online': subDevice.isOnline, 'offline': !subDevice.isOnline}"></view>
                    </view>
                    <view class="sub-device-battery">
                      <view class="battery-bar-mini">
                        <view class="battery-fill" :style="{width: subDevice.battery + '%', backgroundColor: getBatteryColor(subDevice.battery)}"></view>
                      </view>
                      <text class="battery-text-mini" :class="{'battery-low': subDevice.battery < 20}" :style="{color: getBatteryTextColor(subDevice.battery)}">{{subDevice.battery}}%</text>
                    </view>
                  </view>
                  
                  <view class="show-more-btn" v-if="device.devices.length > 3" @click.stop="showDeviceDetail(device)">
                    <text>查看详情 ({{device.devices.length}}个设备)</text>
                  </view>
                </view>
              </view>
              
              <!-- Main control button -->
              <view class="main-control">
                <view class="control-ring" :class="{'active-ring': device.valveOpen && device.isOnline}">
                  <button 
                    class="control-button" 
                    :class="{
                      'active': device.valveOpen, 
                      'inactive': !device.valveOpen, 
                      'warning': device.alarmStatus === 'warning',
                      'disabled': !device.isOnline || !device.valveOpen || device.alarmStatus === 'warning'
                    }"
                    @click="toggleValve(device)"
                    :disabled="!device.isOnline || !device.valveOpen || device.alarmStatus === 'warning'"
                  >
                    <!-- <view class="button-icon">
                      <uniIcons :type="device.valveOpen ? 'checkbox-filled' : 'closeempty'" size="60" color="currentColor"></uniIcons>
                    </view> -->
                    <text class="button-text">{{device.valveOpen ? '阀门已开启' : '阀门已关闭'}}</text>
                    <text class="button-subtext">{{device.isOnline ? (device.valveOpen ? '点击关闭阀门' : '') : '设备离线'}}</text>
                  </button>
                </view>
              
                <view class="alarm-explanation-container">
                  <view class="alarm-explanation" v-if="device.alarmStatus === 'warning'">
                    设备异常，阀门已自动关闭
                  </view>
                </view>
                
                <!-- 扫码开锁按钮 -->
                <button class="scan-unlock-btn" @click="handleScanToUnlock(device)">
                  <uniIcons type="scan" size="16" color="#FFFFFF"></uniIcons>
                  <text>扫码开锁</text>
                </button>
              </view>
              
            </view>
            
            <view class="block-footer">
              <view class="footer-actions">
                <!-- 删除授权管理按钮 -->
                <button class="action-btn" @click="showDeviceDetail(device)">
                  <text>查看详情</text>
                </button>
                <button class="action-btn group-btn" @click="showDeviceGroupOptions(device)">
                  <text v-if="!device.isGroup">设备组</text>
                  <text v-else>组管理</text>
                </button>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>
      
      <!-- Custom pagination dots -->
      <view class="pagination-dots">
        <view 
          v-for="(device, index) in deviceList" 
          :key="index" 
          :class="['pagination-dot', {'active': currentDeviceIndex === index}]"
          @click="changePage(index)"
        ></view>
      </view>
    </view>
    
    <!-- Device Detail Drawer -->
    <view class="detail-drawer" :class="{'drawer-show': showDrawer}" @click.stop>
      <view class="drawer-mask" @click="closeDrawer"></view>
      <view class="drawer-content">
        <view class="drawer-header">
          <view class="drawer-title">设备详情</view>
          <view class="drawer-close" @click="closeDrawer">
            <text class="close-icon">×</text>
          </view>
        </view>
        
        <scroll-view scroll-y class="drawer-body">
          <!-- Single Device Details -->
          <template v-if="selectedDevice && !selectedDevice.isGroup">
            <view class="detail-section">
              <view class="detail-title">基本信息</view>
              <view class="detail-item">
                <text class="detail-label">设备名称</text>
                <text class="detail-value">{{selectedDevice.name}}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">气瓶编码</text>
                <text class="detail-value">{{selectedDevice.code}}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">报警器编号</text>
                <text class="detail-value">{{selectedDevice.alarmCode || '未设置'}}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">设备状态</text>
                <text class="detail-value" :class="{'text-success': selectedDevice.isOnline, 'text-muted': !selectedDevice.isOnline}">
                  {{selectedDevice.isOnline ? '在线' : '离线'}}
                </text>
              </view>
              <view class="detail-item">
                <text class="detail-label">阀门状态</text>
                <text class="detail-value" :class="{'text-primary': selectedDevice.valveOpen, 'text-muted': !selectedDevice.valveOpen}">
                  {{selectedDevice.valveOpen ? '开启' : '关闭'}}
                </text>
              </view>
            </view>
            
            <view class="detail-section">
              <view class="detail-title">设备信息</view>
              <view class="detail-item">
                <text class="detail-label">气瓶编码</text>
                <text class="detail-value">{{selectedDevice.code}} <text v-if="selectedDevice.codeStatus" class="status-tag normal">{{selectedDevice.codeStatus}}</text></text>
              </view>
              <view class="detail-item">
                <text class="detail-label">报警器编号</text>
                <text class="detail-value">{{selectedDevice.alarmCode || '未设置'}} <text v-if="selectedDevice.alarmCodeStatus" class="status-tag normal">{{selectedDevice.alarmCodeStatus}}</text></text>
              </view>
              <view class="detail-item">
                <text class="detail-label">充装单位</text>
                <text class="detail-value">{{selectedDevice.fillingCompany || '未设置'}}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">充装时间</text>
                <text class="detail-value">{{selectedDevice.fillingDate || '未设置'}}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">充装重量</text>
                <text class="detail-value">{{selectedDevice.fillingWeight || '未设置'}}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">报警器状态</text>
                <text class="detail-value" :class="{'text-success': selectedDevice.alarmStatus === 'normal', 'text-error': selectedDevice.alarmStatus === 'warning'}">
                  {{selectedDevice.alarmStatus === 'normal' ? '正常' : '异常'}}
                </text>
              </view>
              <view class="detail-item" v-if="selectedDevice && !selectedDevice.isGroup">
                <text class="detail-label">瓶阀电量</text>
                <view class="detail-value">
                  <view class="battery-bar-detail">
                    <view class="battery-fill" :style="{width: selectedDevice.battery + '%', backgroundColor: getBatteryColor(selectedDevice.battery)}"></view>
                  </view>
                  <text :class="{'battery-low': selectedDevice.battery < 20}" :style="{color: getBatteryTextColor(selectedDevice.battery), marginLeft: '10rpx'}">
                    {{selectedDevice.battery}}% ({{getBatteryStatus(selectedDevice.battery)}})
                  </text>
                  <uniIcons v-if="selectedDevice.battery < 20" type="notification-filled" size="16" color="#FF3B30" class="battery-low" style="margin-left: 8rpx;"></uniIcons>
                </view>
              </view>
              <view class="detail-item">
                <text class="detail-label">设备型号</text>
                <text class="detail-value">GLP-2000</text>
              </view>
            </view>
            
            <view class="detail-section">
              <view class="detail-title">操作记录</view>
              <view class="operation-list">
                <view class="operation-item">
                  <view class="operation-icon success">
                    <uniIcons type="checkmarkempty" size="20" color="#FFFFFF"></uniIcons>
                  </view>
                  <view class="operation-info">
                    <view class="operation-title">阀门开启</view>
                    <view class="operation-time">2023-12-05 08:30:22</view>
                  </view>
                </view>
                <view class="operation-item">
                  <view class="operation-icon error">
                    <uniIcons type="closeempty" size="20" color="#FFFFFF"></uniIcons>
                  </view>
                  <view class="operation-info">
                    <view class="operation-title">阀门关闭</view>
                    <view class="operation-time">2023-12-04 19:45:15</view>
                  </view>
                </view>
                <view class="operation-item">
                  <view class="operation-icon warning">
                    <uniIcons type="notification" size="20" color="#FFFFFF"></uniIcons>
                  </view>
                  <view class="operation-info">
                    <view class="operation-title">报警器触发</view>
                    <view class="operation-time">2023-12-03 14:21:36</view>
                  </view>
                </view>
              </view>
            </view>
          </template>
          
          <!-- Device Group Details -->
          <template v-else-if="selectedDevice && selectedDevice.isGroup">
            <view class="detail-section">
              <view class="detail-title">设备组信息</view>
              <view class="devices-list">
                <view class="device-card" v-for="(device, index) in selectedDevice.devices" :key="index">
                  <view class="device-card-header">
                    <view class="device-card-name">{{device.name}}</view>
                    <view class="device-card-status" :class="{'online': device.isOnline, 'offline': !device.isOnline}">
                      {{device.isOnline ? '在线' : '离线'}}
                    </view>
                  </view>
                  
                  <view class="device-card-content">
                    <view class="device-card-info">
                      <view class="card-info-item">
                        <text class="card-info-label">气瓶编码:</text>
                        <text class="card-info-value">{{device.code}} <text v-if="device.codeStatus" class="status-tag normal">{{device.codeStatus}}</text></text>
                      </view>
                      <view class="card-info-item">
                        <text class="card-info-label">报警器编号:</text>
                        <text class="card-info-value">{{device.alarmCode || '未设置'}} <text v-if="device.alarmCodeStatus" class="status-tag normal">{{device.alarmCodeStatus}}</text></text>
                      </view>
                      <view class="card-info-item">
                        <text class="card-info-label">充装单位:</text>
                        <text class="card-info-value">{{device.fillingCompany || '未设置'}}</text>
                      </view>
                      <view class="card-info-item">
                        <text class="card-info-label">充装时间:</text>
                        <text class="card-info-value">{{device.fillingDate || '未设置'}}</text>
                      </view>
                      <view class="card-info-item">
                        <text class="card-info-label">充装重量:</text>
                        <text class="card-info-value">{{device.fillingWeight || '未设置'}}</text>
                      </view>
                      <view class="card-info-item">
                        <text class="card-info-label">报警器:</text>
                        <text class="card-info-value" :class="{'text-success': device.alarmStatus === 'normal', 'text-error': device.alarmStatus === 'warning'}">
                          {{device.alarmStatus === 'normal' ? '正常' : '异常'}}
                        </text>
                      </view>
                      <view class="card-info-item">
                        <text class="card-info-label">阀门状态:</text>
                        <text class="card-info-value" :class="{'text-primary': device.valveOpen, 'text-muted': !device.valveOpen}">
                          {{device.valveOpen ? '开启' : '关闭'}}
                        </text>
                      </view>
                      <view class="card-info-item">
                        <text class="card-info-label">电量状态:</text>
                        <view class="card-info-value">
                          <view class="battery-bar-card">
                            <view class="battery-fill" :style="{width: device.battery + '%', backgroundColor: getBatteryColor(device.battery)}"></view>
                          </view>
                          <text :class="{'battery-low': device.battery < 20}" :style="{color: getBatteryTextColor(device.battery), marginLeft: '10rpx', fontSize: '24rpx'}">
                            {{device.battery}}%
                          </text>
                          <view class="battery-icon-warning" v-if="device.battery < 20">
                            <uniIcons type="notification-filled" size="14" color="#FF3B30"></uniIcons>
                          </view>
                        </view>
                      </view>
                      <!-- 添加异常状态在电量状态下方 -->
                      <view class="card-info-item" v-if="device.alarmStatus === 'warning'">
                        <text class="card-info-label">异常状态:</text>
                        <view class="card-info-value">
                          <text class="text-error">设备异常，阀门已自动关闭</text>
                        </view>
                      </view>
                    </view>
                    
                    <button 
                      class="device-card-btn" 
                      :class="{
                        'active': device.valveOpen, 
                        'inactive': !device.valveOpen, 
                        'warning': device.alarmStatus === 'warning',
                        'disabled': !device.isOnline || !device.valveOpen || device.alarmStatus === 'warning'
                      }"
                      @click="toggleDeviceInGroup(device)"
                      :disabled="!device.isOnline || !device.valveOpen || device.alarmStatus === 'warning'"
                    >
                      <view class="btn-icon" v-if="!device.valveOpen">
                        <uniIcons type="locked" size="14" color="#fff"></uniIcons>
                      </view>
                      {{device.valveOpen ? '关闭阀门' : '已关闭'}}
                    </button>
                    
                    <!-- 移除按钮下方的异常状态 -->
                  </view>
                </view>
              </view>
            </view>
          </template>
        </scroll-view>
      </view>
    </view>
    
    <!-- Add Device Modal -->
    <view class="modal" v-if="showAddDeviceModal">
      <view class="modal-mask" @click="closeAddDeviceModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">添加设备</text>
          <view class="modal-close" @click="closeAddDeviceModal">
            <uniIcons type="closeempty" size="24" color="#666"></uniIcons>
          </view>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">设备名称</text>
            <input class="form-input" v-model="newDevice.name" placeholder="请输入设备名称" />
          </view>
          <view class="form-item">
            <text class="form-label">气瓶编码</text>
            <input class="form-input" v-model="newDevice.code" placeholder="请输入气瓶编码" />
          </view>
          <view class="form-item">
            <text class="form-label">报警器编号</text>
            <input class="form-input" v-model="newDevice.alarmCode" placeholder="请输入报警器编号" />
          </view>
          <view class="form-item">
            <text class="form-label">购买日期</text>
            <picker 
              mode="date" 
              :value="newDevice.purchaseDate" 
              @change="dateChange" 
              class="form-picker"
            >
              <view class="picker-value">
                {{newDevice.purchaseDate || '请选择购买日期'}}
              </view>
            </picker>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="modal-btn secondary" @click="closeAddDeviceModal">取消</button>
          <button class="modal-btn primary" @click="saveNewDevice">保存</button>
        </view>
      </view>
    </view>
    
    <!-- Create Group Modal -->
    <view class="modal" v-if="showCreateGroupModal">
      <view class="modal-mask" @click="closeCreateGroupModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">创建设备组</text>
          <view class="modal-close" @click="closeCreateGroupModal">
            <uniIcons type="closeempty" size="24" color="#666"></uniIcons>
          </view>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">组名称</text>
            <input class="form-input" v-model="newGroup.name" placeholder="请输入设备组名称" />
          </view>
          
          <view class="form-item">
            <text class="form-label">选择设备</text>
            <view class="device-checklist">
              <view 
                class="device-check-item" 
                v-for="(device, index) in availableDevices" 
                :key="index"
                :class="{'selected': selectedDeviceIds.includes(device.id)}"
                @click="toggleDeviceSelection(device.id)"
              >
                <text class="device-check-name">{{device.name}}</text>
                <view class="device-check-icon">
                  <uniIcons :type="selectedDeviceIds.includes(device.id) ? 'checkbox-filled' : 'circle'" size="20" color="#2979ff"></uniIcons>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="modal-btn secondary" @click="closeCreateGroupModal">取消</button>
          <button class="modal-btn primary" @click="saveNewGroup">创建</button>
        </view>
      </view>
    </view>
    
    <!-- Group Action Sheet -->
    <view class="action-sheet" v-if="showGroupActionSheet">
      <view class="action-sheet-mask" @click="closeGroupActionSheet"></view>
      <view class="action-sheet-content">
        <view class="action-sheet-title">设备组操作</view>
        <!-- 移除这里的授权管理和扫码开锁按钮 -->
        <view class="action-sheet-item" @click="createGroup" v-if="!currentDevice.isGroup">
          <text>创建新设备组</text>
        </view>
        <view class="action-sheet-item" @click="addToGroup" v-if="!currentDevice.isGroup">
          <text>添加到现有组</text>
        </view>
        <view class="action-sheet-item" @click="removeFromGroup" v-if="!currentDevice.isGroup && isDeviceInGroup(currentDevice.id)">
          <text>从当前组中移除</text>
        </view>
        <view class="action-sheet-item" @click="showEditGroupName" v-if="currentDevice.isGroup">
          <text>修改名称</text>
        </view>
        <view class="action-sheet-item" @click="dissolveGroup" v-if="currentDevice.isGroup">
          <text>解散设备组</text>
        </view>
        <view class="action-sheet-cancel" @click="closeGroupActionSheet">取消</view>
      </view>
    </view>
    
    <!-- Group Selection Modal -->
    <view class="modal" v-if="showGroupSelectionModal">
      <view class="modal-mask" @click="closeGroupSelectionModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">选择设备组</text>
          <view class="modal-close" @click="closeGroupSelectionModal">
            <uniIcons type="closeempty" size="24" color="#666"></uniIcons>
          </view>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">选择要添加到的设备组</text>
            <view class="group-list">
              <view 
                class="group-list-item" 
                v-for="(group, index) in deviceGroups" 
                :key="index"
                @click="selectGroup(group.id)"
              >
                <text class="group-list-name">{{group.name}}</text>
                <view class="group-list-count">{{group.deviceCount}}个设备</view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="modal-btn secondary" @click="closeGroupSelectionModal">取消</button>
        </view>
      </view>
    </view>
    
    <!-- 添加修改设备组名称的弹窗 -->
    <view class="modal" v-if="showEditGroupNameModal">
      <view class="modal-mask" @click="cancelEditGroupName"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">修改设备组名称</text>
          <view class="modal-close" @click="cancelEditGroupName">
            <text class="close-icon">×</text>
          </view>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">设备组名称</text>
            <input class="form-input" v-model="editingGroupName" placeholder="请输入设备组名称" />
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="modal-btn secondary" @click="cancelEditGroupName">取消</button>
          <button class="modal-btn primary" @click="saveGroupName">保存</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import uniIcons from '@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue'

export default {
  components: {
    uniIcons
  },
  data() {
    return {
      deviceList: [],
      currentDeviceIndex: 0,
      showDrawer: false,
      selectedDevice: null,
      showAddDeviceModal: false,
      showCreateGroupModal: false,
      showGroupActionSheet: false,
      showGroupSelectionModal: false,
      currentDevice: null,
      newDevice: {
        name: '',
        code: '',
        alarmCode: '',
        purchaseDate: ''
      },
      newGroup: {
        name: ''
      },
      selectedDeviceIds: [],
      isEditingGroupName: false,
      editingGroupName: '',
      showEditGroupNameModal: false,
    }
  },
  
  computed: {
    hasOnlineDevices() {
      if (!this.selectedDevice || !this.selectedDevice.isGroup) return false
      return this.selectedDevice.devices.some(device => device.isOnline)
    },
    
    availableDevices() {
      // 返回所有非组设备
      return this.deviceList.filter(device => !device.isGroup && !this.isDeviceInGroup(device.id))
    },
    
    deviceGroups() {
      // 返回所有设备组
      return this.deviceList.filter(device => device.isGroup)
    }
  },
  
  onLoad() {
    this.loadDeviceList()
  },
  
  methods: {
    // 根据电量获取颜色
    getBatteryColor(battery) {
      if (battery >= 60) return '#4CD964'; // 绿色
      if (battery >= 30) return '#FF9500'; // 橙色
      return '#FF3B30'; // 红色
    },
    
    // 根据电量获取文字颜色
    getBatteryTextColor(battery) {
      if (battery >= 60) return '#4CD964'; // 绿色
      if (battery >= 30) return '#FF9500'; // 橙色
      return '#FF3B30'; // 红色
    },
    
    // 根据电量获取状态描述
    getBatteryStatus(battery) {
      if (battery >= 80) return '电量充足';
      if (battery >= 60) return '电量良好';
      if (battery >= 40) return '电量中等';
      if (battery >= 20) return '电量较低';
      return '电量不足，请及时更换';
    },
    
    // 加载设备列表
    loadDeviceList() {
      // 实际应用中应调用API获取设备列表
      // 这里直接赋值，不使用延时
      this.deviceList = [
        {
          id: 1,
          name: '厨房主气瓶',
          isGroup: false,
          code: 'LPG12345678',
          alarmCode: 'ALARM12345',
          purchaseDate: '2023-09-15',
          alarmStatus: 'normal',
          isOnline: true,
          valveOpen: true,
          battery: 85, // 电量百分比
          codeStatus: '正常',
          alarmCodeStatus: '正常',
          fillingCompany: '充装单位A',
          fillingDate: '2023-09-15',
          fillingWeight: '10kg'
        },
        {
          id: 2,
          name: '备用气瓶',
          isGroup: false,
          code: 'LPG87654321',
          alarmCode: 'ALARM67890',
          purchaseDate: '2023-11-20',
          alarmStatus: 'warning',
          isOnline: true,
          valveOpen: false, // 异常状态下阀门强制关闭
          battery: 32, // 电量百分比
          codeStatus: '正常',
          alarmCodeStatus: '异常',
          fillingCompany: '充装单位B',
          fillingDate: '2023-11-20',
          fillingWeight: '8kg'
        },
        {
          id: 3,
          name: '餐厅气瓶组',
          isGroup: true,
          deviceCount: 3,
          isOnline: true,
          valveOpen: false,
          battery: null, // 组没有直接电量
          devices: [
            {
              id: 31,
              name: '餐厅气瓶1',
              code: 'LPG11112222',
              purchaseDate: '2023-08-10',
              alarmStatus: 'normal',
              isOnline: true,
              valveOpen: true,
              battery: 78,
              codeStatus: '正常',
              alarmCodeStatus: '正常',
              fillingCompany: '充装单位C',
              fillingDate: '2023-08-10',
              fillingWeight: '12kg'
            },
            {
              id: 32,
              name: '餐厅气瓶2',
              code: 'LPG33334444',
              purchaseDate: '2023-08-10',
              alarmStatus: 'warning',
              isOnline: true,
              valveOpen: false, // 异常状态下阀门强制关闭
              battery: 45,
              codeStatus: '异常',
              alarmCodeStatus: '正常',
              fillingCompany: '充装单位D',
              fillingDate: '2023-08-10',
              fillingWeight: '10kg'
            },
            {
              id: 33,
              name: '餐厅气瓶3',
              code: 'LPG55556666',
              purchaseDate: '2023-08-10',
              alarmStatus: 'normal',
              isOnline: false,
              valveOpen: false,
              battery: 13,
              codeStatus: '正常',
              alarmCodeStatus: '正常',
              fillingCompany: '充装单位E',
              fillingDate: '2023-08-10',
              fillingWeight: '9kg'
            },
            {
              id: 34,
              name: '餐厅气瓶4',
              code: 'LPG77778888',
              purchaseDate: '2023-08-10',
              alarmStatus: 'normal',
              isOnline: true,
              valveOpen: true,
              battery: 92,
              codeStatus: '正常',
              alarmCodeStatus: '正常',
              fillingCompany: '充装单位F',
              fillingDate: '2023-08-10',
              fillingWeight: '11kg'
            },
            {
              id: 35,
              name: '餐厅气瓶5',
              code: 'LPG99990000',
              purchaseDate: '2023-08-10',
              alarmStatus: 'normal',
              isOnline: true,
              valveOpen: false,
              battery: 67,
              codeStatus: '正常',
              alarmCodeStatus: '正常',
              fillingCompany: '充装单位G',
              fillingDate: '2023-08-10',
              fillingWeight: '10kg'
            }
          ]
        }
      ]
      
      // 确保异常状态的设备阀门关闭
      this.deviceList.forEach(device => {
        if (!device.isGroup) {
          if (device.alarmStatus === 'warning') {
            device.valveOpen = false
          }
        } else if (device.devices) {
          device.devices.forEach(subDevice => {
            if (subDevice.alarmStatus === 'warning') {
              subDevice.valveOpen = false
            }
          })
          // 更新组状态
          device.valveOpen = device.devices.every(d => d.isOnline && d.valveOpen)
        }
      })
    },
    
    // 添加设备
    addDevice() {
      this.showAddDeviceModal = true
    },
    
    // 关闭添加设备弹窗
    closeAddDeviceModal() {
      this.showAddDeviceModal = false
      this.resetNewDevice()
    },
    
    // 重置新设备表单
    resetNewDevice() {
      this.newDevice = {
        name: '',
        code: '',
        alarmCode: '',
        purchaseDate: ''
      }
    },
    
    // 保存新设备
    saveNewDevice() {
      if (!this.newDevice.name || !this.newDevice.code || !this.newDevice.purchaseDate) {
        uni.showToast({
          title: '请填写完整信息',
          icon: 'none'
        })
        return
      }
      
      uni.showLoading({
        title: '保存中'
      })
      
      // 模拟API调用
      setTimeout(() => {
        // 创建新设备对象
        const device = {
          id: Date.now(), // 使用时间戳作为临时ID
          name: this.newDevice.name,
          isGroup: false,
          code: this.newDevice.code,
          purchaseDate: this.newDevice.purchaseDate,
          alarmStatus: 'normal',
          isOnline: true,
          valveOpen: false,
          battery: 100 // 默认电量
        }
        
        // 添加到设备列表
        this.deviceList.push(device)
        
        uni.hideLoading()
        uni.showToast({
          title: '设备添加成功',
          icon: 'success'
        })
        
        // 关闭弹窗并重置表单
        this.closeAddDeviceModal()
      }, 1000)
    },
    
    // 日期选择器变化
    dateChange(e) {
      this.newDevice.purchaseDate = e.detail.value
    },
    
    // 显示设备组选项
    showDeviceGroupOptions(device) {
      this.currentDevice = device
      this.showGroupActionSheet = true
    },
    
    // 关闭设备组操作菜单
    closeGroupActionSheet() {
      this.showGroupActionSheet = false
    },
    
    // 创建设备组
    createGroup() {
      this.closeGroupActionSheet()
      
      // 如果当前设备不是组，则预先选择它
      if (this.currentDevice && !this.currentDevice.isGroup) {
        this.selectedDeviceIds = [this.currentDevice.id]
      } else {
        this.selectedDeviceIds = []
      }
      
      this.showCreateGroupModal = true
    },
    
    // 关闭创建组弹窗
    closeCreateGroupModal() {
      this.showCreateGroupModal = false
      this.resetNewGroup()
    },
    
    // 重置新组表单
    resetNewGroup() {
      this.newGroup = {
        name: ''
      }
      this.selectedDeviceIds = []
    },
    
    // 切换设备选择
    toggleDeviceSelection(deviceId) {
      const index = this.selectedDeviceIds.indexOf(deviceId)
      if (index === -1) {
        this.selectedDeviceIds.push(deviceId)
      } else {
        this.selectedDeviceIds.splice(index, 1)
      }
    },
    
    // 保存新组
    saveNewGroup() {
      if (!this.newGroup.name || this.selectedDeviceIds.length === 0) {
        uni.showToast({
          title: '请填写组名称并选择至少一个设备',
          icon: 'none'
        })
        return
      }
      
      uni.showLoading({
        title: '创建中'
      })
      
      // 模拟API调用
      setTimeout(() => {
        // 从设备列表中获取选中的设备
        const selectedDevices = this.deviceList
          .filter(device => this.selectedDeviceIds.includes(device.id))
          .map(device => {
            // 创建副本，避免引用原对象
            return JSON.parse(JSON.stringify(device))
          })
        
        // 创建新组对象
        const group = {
          id: Date.now(), // 使用时间戳作为临时ID
          name: this.newGroup.name,
          isGroup: true,
          deviceCount: selectedDevices.length,
          isOnline: selectedDevices.some(device => device.isOnline),
          valveOpen: selectedDevices.every(device => device.valveOpen),
          devices: selectedDevices
        }
        
        // 从设备列表中移除选中的设备
        this.deviceList = this.deviceList.filter(device => !this.selectedDeviceIds.includes(device.id))
        
        // 添加新组到设备列表
        this.deviceList.unshift(group)
        
        uni.hideLoading()
        uni.showToast({
          title: '设备组创建成功',
          icon: 'success'
        })
        
        // 关闭弹窗并重置表单
        this.closeCreateGroupModal()
      }, 1000)
    },
    
    // 添加到组
    addToGroup() {
      if (!this.currentDevice) return
      
      this.closeGroupActionSheet()
      
      // 检查是否有可用的组
      if (this.deviceGroups.length === 0) {
        uni.showToast({
          title: '没有可用的设备组，请先创建',
          icon: 'none'
        })
        return
      }
      
      this.showGroupSelectionModal = true
    },
    
    // 关闭组选择弹窗
    closeGroupSelectionModal() {
      this.showGroupSelectionModal = false
    },
    
    // 选择组
    selectGroup(groupId) {
      if (!this.currentDevice) return
      
      uni.showLoading({
        title: '添加中'
      })
      
      // 模拟API调用
      setTimeout(() => {
        // 找到选中的组
        const groupIndex = this.deviceList.findIndex(device => device.id === groupId)
        if (groupIndex === -1) {
          uni.hideLoading()
          return
        }
        
        // 创建设备副本
        const deviceCopy = JSON.parse(JSON.stringify(this.currentDevice))
        
        // 添加设备到组
        this.deviceList[groupIndex].devices.push(deviceCopy)
        this.deviceList[groupIndex].deviceCount = this.deviceList[groupIndex].devices.length
        
        // 更新组的状态
        this.deviceList[groupIndex].isOnline = this.deviceList[groupIndex].devices.some(device => device.isOnline)
        this.deviceList[groupIndex].valveOpen = this.deviceList[groupIndex].devices.every(device => device.valveOpen)
        
        // 从设备列表中移除设备
        const deviceIndex = this.deviceList.findIndex(device => device.id === this.currentDevice.id)
        if (deviceIndex !== -1) {
          this.deviceList.splice(deviceIndex, 1)
        }
        
        uni.hideLoading()
        uni.showToast({
          title: '添加到设备组成功',
          icon: 'success'
        })
        
        // 关闭弹窗
        this.closeGroupSelectionModal()
      }, 1000)
    },
    
    // 检查设备是否在某个组中
    isDeviceInGroup(deviceId) {
      // 检查设备是否在任何设备组中
      for (let i = 0; i < this.deviceList.length; i++) {
        if (this.deviceList[i].isGroup && this.deviceList[i].devices) {
          if (this.deviceList[i].devices.some(d => d.id === deviceId)) {
            return true
          }
        }
      }
      return false
    },
    
    // 查找设备所在的组
    findDeviceGroup(deviceId) {
      for (let i = 0; i < this.deviceList.length; i++) {
        if (this.deviceList[i].isGroup && this.deviceList[i].devices) {
          const deviceIndex = this.deviceList[i].devices.findIndex(d => d.id === deviceId)
          if (deviceIndex !== -1) {
            return {
              group: this.deviceList[i],
              groupIndex: i,
              deviceIndex: deviceIndex
            }
          }
        }
      }
      return null
    },
    
    // 从组中移除设备
    removeFromGroup() {
      if (!this.currentDevice) return
      
      const result = this.findDeviceGroup(this.currentDevice.id)
      if (!result) {
        this.closeGroupActionSheet()
        return
      }
      
      uni.showModal({
        title: '确认操作',
        content: `确定要将 ${this.currentDevice.name} 从 ${result.group.name} 中移除吗？`,
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '移除中'
            })
            
            // 模拟API调用
            setTimeout(() => {
              // 创建设备副本
              const deviceCopy = JSON.parse(JSON.stringify(result.group.devices[result.deviceIndex]))
              
              // 从组中移除设备
              result.group.devices.splice(result.deviceIndex, 1)
              result.group.deviceCount = result.group.devices.length
              
              // 如果组变为空，则移除组
              if (result.group.deviceCount === 0) {
                this.deviceList.splice(result.groupIndex, 1)
              } else {
                // 更新组状态
                result.group.isOnline = result.group.devices.some(device => device.isOnline)
                result.group.valveOpen = result.group.devices.every(device => device.valveOpen)
              }
              
              // 将设备添加到设备列表
              this.deviceList.push(deviceCopy)
              
              uni.hideLoading()
              uni.showToast({
                title: '已从设备组中移除',
                icon: 'success'
              })
              
              this.closeGroupActionSheet()
            }, 1000)
          } else {
            this.closeGroupActionSheet()
          }
        }
      })
    },
    
    // 解散设备组
    dissolveGroup() {
      if (!this.currentDevice || !this.currentDevice.isGroup) return
      
      uni.showModal({
        title: '确认操作',
        content: `确定要解散设备组 ${this.currentDevice.name} 吗？组内设备将恢复为独立设备。`,
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '解散中'
            })
            
            // 模拟API调用
            setTimeout(() => {
              // 找到组索引
              const groupIndex = this.deviceList.findIndex(device => device.id === this.currentDevice.id)
              if (groupIndex === -1) {
                uni.hideLoading()
                this.closeGroupActionSheet()
                return
              }
              
              // 获取组内设备
              const devices = JSON.parse(JSON.stringify(this.deviceList[groupIndex].devices))
              
              // 移除组
              this.deviceList.splice(groupIndex, 1)
              
              // 将组内设备添加到设备列表
              this.deviceList = [...this.deviceList, ...devices]
              
              uni.hideLoading()
              uni.showToast({
                title: '设备组已解散',
                icon: 'success'
              })
              
              this.closeGroupActionSheet()
            }, 1000)
          } else {
            this.closeGroupActionSheet()
          }
        }
      })
    },
    
    // 显示设备详情
    showDeviceDetail(device) {
      this.selectedDevice = device
      this.showDrawer = true
    },
    
    // 关闭详情抽屉
    closeDrawer() {
      this.showDrawer = false
    },
    
    // 切换阀门状态
    toggleValve(device) {
      if (!device.isOnline) {
        uni.showToast({
          title: '设备离线，无法控制',
          icon: 'none'
        })
        return
      }
      
      // 检查报警状态，如果是异常则不允许操作
      if (!device.isGroup && device.alarmStatus === 'warning') {
        uni.showToast({
          title: '设备异常，不可操作阀门',
          icon: 'none'
        })
        return
      }
      
      // 只允许关闭阀门，不允许开启
      if (!device.valveOpen) {
        uni.showToast({
          title: '不允许开启阀门',
          icon: 'none'
        })
        return
      }
      
      uni.showModal({
        title: '确认操作',
        content: '确定要关闭阀门吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '操作中'
            })
            
            // 模拟API调用
            setTimeout(() => {
              // 更新本地状态
              if (device.isGroup) {
                // 如果是设备组，更新所有子设备
                device.devices.forEach(subDevice => {
                  if (subDevice.isOnline && subDevice.alarmStatus !== 'warning') {
                    subDevice.valveOpen = false
                  }
                })
              }
              
              device.valveOpen = false
              
              // 如果抽屉显示的是当前设备，同步更新抽屉中显示的数据
              if (this.selectedDevice && this.selectedDevice.id === device.id) {
                this.selectedDevice.valveOpen = false
              }
              
              uni.hideLoading()
              uni.showToast({
                title: '阀门已关闭',
                icon: 'success'
              })
            }, 1000)
          }
        }
      })
    },
    
    // 从抽屉中切换阀门状态
    toggleValveFromDrawer() {
      if (this.selectedDevice && this.selectedDevice.valveOpen) {
        // 找到实际设备对象，因为selectedDevice可能是副本
        let device
        if (this.selectedDevice.id >= 30) {
          // 是子设备
          for (let i = 0; i < this.deviceList.length; i++) {
            if (this.deviceList[i].isGroup && this.deviceList[i].devices) {
              device = this.deviceList[i].devices.find(d => d.id === this.selectedDevice.id)
              if (device) break
            }
          }
        } else {
          // 是主设备
          device = this.deviceList.find(d => d.id === this.selectedDevice.id)
        }
        
        if (device) {
          this.toggleValve(device)
        }
      } else {
        uni.showToast({
          title: '不允许开启阀门',
          icon: 'none'
        })
      }
    },
    
    // 切换设备组中单个设备的阀门状态
    toggleDeviceInGroup(device) {
      if (!device.isOnline) {
        uni.showToast({
          title: '设备离线，无法控制',
          icon: 'none'
        })
        return
      }
      
      // 检查报警状态，如果是异常则不允许操作
      if (device.alarmStatus === 'warning') {
        uni.showToast({
          title: '设备异常，不可操作阀门',
          icon: 'none'
        })
        return
      }
      
      // 只允许关闭阀门，不允许开启
      if (!device.valveOpen) {
        uni.showToast({
          title: '不允许开启阀门',
          icon: 'none'
        })
        return
      }
      
      uni.showModal({
        title: '确认操作',
        content: '确定要关闭阀门吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '操作中'
            })
            
            // 模拟API调用
            setTimeout(() => {
              // 更新设备状态
              device.valveOpen = false
              
              // 检查组内所有设备状态
              this.updateGroupStatus()
              
              uni.hideLoading()
              uni.showToast({
                title: '阀门已关闭',
                icon: 'success'
              })
            }, 1000)
          }
        }
      })
    },
    
    // 批量控制设备组内所有设备
    batchToggleGroup(openValve) {
      // 如果试图开启阀门，则阻止
      if (openValve) {
        uni.showToast({
          title: '不允许开启阀门',
          icon: 'none'
        })
        return
      }
      
      uni.showModal({
        title: '确认操作',
        content: '确定要关闭所有在线设备的阀门吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '操作中'
            })
            
            // 模拟API调用
            setTimeout(() => {
              // 更新所有在线设备的状态
              if (this.selectedDevice && this.selectedDevice.isGroup) {
                this.selectedDevice.devices.forEach(device => {
                  if (device.isOnline && device.alarmStatus !== 'warning') {
                    device.valveOpen = false
                  }
                })
                
                // 更新组状态
                this.updateGroupStatus()
              }
              
              uni.hideLoading()
              uni.showToast({
                title: '已关闭所有在线设备的阀门',
                icon: 'success'
              })
            }, 1000)
          }
        }
      })
    },
    
    // 更新设备组状态
    updateGroupStatus() {
      // 同步设备组的状态
      if (this.selectedDevice && this.selectedDevice.isGroup) {
        // 找到对应的原始设备组
        const originalGroup = this.deviceList.find(d => d.id === this.selectedDevice.id)
        if (originalGroup) {
          // 检查是否所有在线设备都是开启状态
          const allOnlineOpened = originalGroup.devices
            .filter(d => d.isOnline)
            .every(d => d.valveOpen)
          
          // 更新设备组的阀门状态
          originalGroup.valveOpen = allOnlineOpened
          // 同步到选中的设备
          this.selectedDevice.valveOpen = allOnlineOpened
        }
      }
    },
    
    // 处理轮播图切换
    handleSwiperChange(e) {
      this.currentDeviceIndex = e.detail.current
    },
    
    // 点击分页跳转到指定页
    changePage(index) {
      this.currentDeviceIndex = index
    },
    
    // 获取设备组的平均电量
    getAverageBattery(deviceGroup) {
      if (!deviceGroup.isGroup || !deviceGroup.devices || deviceGroup.devices.length === 0) return 0;
      
      const totalBattery = deviceGroup.devices.reduce((sum, device) => sum + device.battery, 0);
      return Math.round(totalBattery / deviceGroup.devices.length);
    },
    
    // 获取设备组的最高电量
    getMaxBattery(deviceGroup) {
      if (!deviceGroup.isGroup || !deviceGroup.devices || deviceGroup.devices.length === 0) return 0;
      
      const batteries = deviceGroup.devices.map(device => device.battery);
      return Math.max(...batteries);
    },
    
    // 获取设备组的最低电量
    getMinBattery(deviceGroup) {
      if (!deviceGroup.isGroup || !deviceGroup.devices || deviceGroup.devices.length === 0) return 0;
      
      const batteries = deviceGroup.devices.map(device => device.battery);
      return Math.min(...batteries);
    },
    
    // 获取电量低的设备数量
    getLowBatteryCount(deviceGroup) {
      if (!deviceGroup.isGroup || !deviceGroup.devices || deviceGroup.devices.length === 0) return 0;
      
      return deviceGroup.devices.filter(device => device.battery < 20).length;
    },
    
    // 开始编辑设备组名称
    startEditGroupName() {
      if (this.selectedDevice && this.selectedDevice.isGroup) {
        this.isEditingGroupName = true
        this.editingGroupName = this.selectedDevice.name
        // 下一帧时自动获取焦点
        this.$nextTick(() => {
          if (this.$refs.groupNameInput) {
            this.$refs.groupNameInput.focus()
          }
        })
      }
    },
    
    // 显示修改设备组名称弹窗
    showEditGroupName() {
      if (this.currentDevice && this.currentDevice.isGroup) {
        this.editingGroupName = this.currentDevice.name
        this.showEditGroupNameModal = true
        this.closeGroupActionSheet()
      }
    },
    
    // 保存设备组名称
    saveGroupName() {
      if (!this.editingGroupName.trim()) {
        uni.showToast({
          title: '名称不能为空',
          icon: 'none'
        })
        return
      }
      
      // 找到原始设备组对象
      const groupIndex = this.deviceList.findIndex(device => device.id === (this.selectedDevice ? this.selectedDevice.id : this.currentDevice.id))
      
      if (groupIndex !== -1) {
        uni.showLoading({
          title: '保存中'
        })
        
        // 模拟API调用
        setTimeout(() => {
          // 更新设备组名称
          this.deviceList[groupIndex].name = this.editingGroupName.trim()
          
          // 如果在详情页中，更新选中设备的名称
          if (this.selectedDevice && this.selectedDevice.isGroup && this.selectedDevice.id === this.deviceList[groupIndex].id) {
            this.selectedDevice.name = this.editingGroupName.trim()
          }
          
          // 重置状态
          this.isEditingGroupName = false
          this.editingGroupName = ''
          this.showEditGroupNameModal = false
          
          uni.hideLoading()
          uni.showToast({
            title: '名称已更新',
            icon: 'success'
          })
        }, 500)
      } else {
        this.cancelEditGroupName()
      }
    },
    
    // 取消编辑设备组名称
    cancelEditGroupName() {
      this.isEditingGroupName = false
      this.editingGroupName = ''
      this.showEditGroupNameModal = false
    },
    
    // 处理授权管理 - 可以保留此方法，但不再调用
    handleAuthManagement() {
      this.closeGroupActionSheet()
      
      // 获取设备数据
      let device = this.currentDevice || this.deviceList[this.currentDeviceIndex]
      
      // 跳转到授权管理页面
      uni.navigateTo({
        url: '/pages/device/authorization?id=' + encodeURIComponent(device ? device.id : ''),
        success: (res) => {
          // 通过eventChannel向被打开页面传送数据
          res.eventChannel.emit('deviceInfo', {
            id: device ? device.id : '',
            name: device ? device.name : '',
            code: device ? device.code : '',
            alarmCode: device ? device.alarmCode : '',
            isGroup: device ? device.isGroup : false,
            deviceCount: device && device.isGroup ? device.deviceCount : 0,
            devices: device && device.isGroup ? device.devices : []
          })
        }
      })
    },
    
    // 处理扫码开锁
    handleScanToUnlock(device) {
      this.closeGroupActionSheet()
      
      // 如果是设备组，弹出选择设备的对话框
      if (device && device.isGroup) {
        // 找出未开锁的设备
        const lockedDevices = device.devices.filter(d => d.isOnline && !d.valveOpen)
        
        if (lockedDevices.length === 0) {
          uni.showToast({
            title: '没有可开锁的设备',
            icon: 'none'
          })
          return
        }
        
        // 显示设备选择对话框
        uni.showActionSheet({
          itemList: lockedDevices.map(d => d.name),
          success: (res) => {
            const selectedDevice = lockedDevices[res.tapIndex]
            // 进入扫码开锁流程
            this.startScanUnlock(selectedDevice)
          }
        })
      } else {
        // 单设备直接开始扫码开锁流程
        this.startScanUnlock(device)
      }
    },
    
          // 开始扫码开锁流程
      startScanUnlock(device) {
        // 直接调用扫码API，不显示toast
        uni.scanCode({
          success: (res) => {
            // 处理扫码结果
            console.log('扫码结果:', res)
            
            // 导航到确认安全页面
            uni.navigateTo({
              url: '/pages/device/safety-confirm?code=' + encodeURIComponent(res.result) + '&deviceId=' + device.id,
              fail: (err) => {
                console.error('导航失败:', err)
                uni.showToast({
                  title: '页面导航失败',
                  icon: 'none'
                })
              }
            })
          },
          fail: (err) => {
            console.error('扫码失败:', err)
            if (err.errMsg.indexOf('cancel') !== -1) {
              uni.showToast({
                title: '已取消扫码',
                icon: 'none'
              })
            } else {
              uni.showToast({
                title: '扫码失败',
                icon: 'none'
              })
            }
          }
        })
      },
  }
}
</script>

<style scoped>
.device-container {
  padding-top: 0;
  background: linear-gradient(180deg, #2979ff, #0d47a1);
  height: 100vh;
  color: #333;
  display: flex;
  flex-direction: column;
}

.header {
  padding: 30rpx 30rpx 15rpx; /* 减小下边距 */
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 36rpx; /* 稍微减小字号 */
  font-weight: bold;
  color: #FFFFFF;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}

.add-device {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background-color: rgba(255,255,255,0.2);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.1);
}

/* 无设备状态 */
.no-device {
  margin-top: 100rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.no-device image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 40rpx;
}

.no-device-text {
  color: #fff;
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

.add-device-btn {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  background-color: rgba(255,255,255,0.2);
  color: #fff;
  border-radius: 40rpx;
  border: 1px solid rgba(255,255,255,0.3);
  backdrop-filter: blur(5px);
}

/* 全屏设备块 */
.fullscreen-blocks {
  /* height: calc(100vh - 90rpx); 减小高度，使内容更紧凑 */
  height: 100%;
  position: relative;
}

.fullscreen-swiper {
  height: 95%;
}

.device-block {
  height: 100%;
  margin: 0 30rpx;
  background-color: rgba(255,255,255,0.85);
  border-radius: 30rpx;
  box-shadow: 0 15rpx 40rpx rgba(0,0,0,0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.device-group {
  background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(240,248,255,0.9));
}

.block-header {
  padding: 25rpx 30rpx 15rpx; /* 减小上下内边距 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid rgba(0,0,0,0.05);
}

.device-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.device-status {
  font-size: 24rpx;
  padding: 4rpx 20rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
}

.status-dot {
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  margin-right: 6rpx;
}

.status-online {
  background-color: rgba(25,190,107,0.1);
  color: var(--success-color);
  border: 1px solid rgba(25,190,107,0.2);
}

.status-online .status-dot {
  background-color: var(--success-color);
  box-shadow: 0 0 5rpx rgba(25,190,107,0.5);
}

.status-offline {
  background-color: rgba(144,147,153,0.1);
  color: var(--info-color);
  border: 1px solid rgba(144,147,153,0.2);
}

.status-offline .status-dot {
  background-color: var(--info-color);
}

/* 整体容器 */
.block-content {
  display: flex;
  flex-direction: column;
  padding: 20rpx 30rpx;
  flex: 1;
  position: relative; /* 添加相对定位 */
}

/* 设备信息区域 */
.device-info {
  flex: 0 0 auto;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
  background-color: rgba(255,255,255,0.5);
  border-radius: 20rpx;
  padding: 20rpx;
  /* max-height: 220rpx;  */
  margin-bottom: 380rpx; /* 为控制区域预留空间 */
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  align-items: center;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #666;
  font-size: 26rpx;
  display: flex;
  align-items: center;
}

.info-label uniIcons {
  margin-right: 8rpx;
}

.info-value {
  font-size: 26rpx;
  font-weight: 500;
}

.alarm-normal {
  color: var(--success-color);
}

.alarm-warning {
  color: var(--error-color);
}

/* 设备组信息区域 */
.device-group-info {
  flex: 0 0 auto;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
  max-height: none; /* 移除高度限制 */
  margin-bottom: 340rpx; /* 为控制区域预留空间 */
}

.group-info-header {
  display: flex;
  justify-content: center;
  margin-bottom: 15rpx; /* 减少间距 */
}

.group-info {
  background: linear-gradient(135deg, #2979ff, #1565c0);
  color: #fff;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  padding: 15rpx 60rpx;
  box-shadow: 0 4rpx 20rpx rgba(41,121,255,0.3);
}

.count {
  font-size: 42rpx;
  font-weight: bold;
  margin-right: 10rpx;
}

.group-devices {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  flex: 0 0 auto;
  max-height: none; /* 移除最大高度限制以去掉滚动条 */
  overflow-y: visible; /* 改为可见，不使用滚动条 */
}

.group-device-item {
  background-color: #f5f5f5;
  padding: 15rpx 20rpx; /* 稍微减小高度以适应更多项 */
  border-radius: 12rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
  border-left: 4rpx solid transparent;
  transition: all 0.2s ease;
}

.group-device-item:active {
  transform: scale(0.98);
}

.group-device-item:last-child {
  margin-bottom: 0;
}

.sub-device-info {
  display: flex;
  align-items: center;
  width: 55%;
}

.sub-device-info text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10rpx;
  font-weight: 500;
  color: #333;
}

.sub-device-battery {
  display: flex;
  align-items: center;
  /* background-color: rgba(255, 255, 255, 0.7); */
  padding: 5rpx 10rpx;
  border-radius: 20rpx;
}

.sub-status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-left: 8rpx;
  flex-shrink: 0;
}

.sub-status-dot.online {
  background-color: var(--success-color);
  box-shadow: 0 0 5rpx rgba(25,190,107,0.5);
}

.sub-status-dot.offline {
  background-color: var(--info-color);
}

.battery-bar-mini {
  width: 60rpx;
  height: 12rpx;
  background-color: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
  display: inline-block;
  vertical-align: middle;
  margin-right: 8rpx;
  border: 1px solid rgba(0,0,0,0.05);
}

.battery-text-mini {
  font-size: 22rpx;
  font-weight: 600;
  vertical-align: middle;
  margin-right: 6rpx;
}

/* 主控制区域 - 使用固定位置 */
.main-control {
  position: absolute; /* 使用绝对定位 */
  top: 60%; /* 垂直居中 */
  left: 0;
  right: 0;
  transform: translateY(-40%); /* 精确居中 */
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1; /* 确保在其他元素上方 */
}

.control-ring {
  width: 260rpx; /* 从300rpx减小到260rpx */
  height: 260rpx; /* 从300rpx减小到260rpx */
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10rpx; /* 从15rpx减小到10rpx */
}

.control-ring.active-ring {
  background: linear-gradient(135deg, rgba(41,121,255,0.15), rgba(21,101,192,0.08)); /* 减轻背景色 */
  box-shadow: 0 0 20rpx rgba(41,121,255,0.25); /* 从30rpx减小到20rpx, 降低透明度 */
}

.control-button {
  width: 220rpx; /* 从260rpx减小到220rpx */
  height: 220rpx; /* 从260rpx减小到220rpx */
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.12); /* 从8rpx 30rpx减小到6rpx 20rpx, 降低透明度 */
  border: none;
  transition: all 0.3s ease;
}

.button-icon {
  margin-bottom: 15rpx;
}

.button-text {
  font-size: 28rpx; /* 从30rpx减小到28rpx */
  font-weight: bold;
  margin-bottom: 4rpx; /* 从6rpx减小到4rpx */
}

.button-subtext {
  font-size: 20rpx; /* 从22rpx减小到20rpx */
  opacity: 0.8;
}

.control-button.active {
  background: linear-gradient(135deg, #2979ff, #1565c0);
  color: #fff;
}

.control-button.inactive {
  background: linear-gradient(135deg, #f44336, #c62828);
  color: #fff;
  box-shadow: 0 0 20rpx rgba(244, 67, 54, 0.6);
}

.control-button.warning {
  background: linear-gradient(135deg, #f44336, #c62828);
  color: #fff;
  box-shadow: 0 0 20rpx rgba(244, 67, 54, 0.6);
  animation: pulseShadow 1.5s infinite;
}

@keyframes pulseShadow {
  0% {
    box-shadow: 0 0 20rpx rgba(244, 67, 54, 0.6);
  }
  50% {
    box-shadow: 0 0 60rpx rgba(244, 67, 54, 0.8);
  }
  100% {
    box-shadow: 0 0 20rpx rgba(244, 67, 54, 0.6);
  }
}

.control-button.disabled {
  /* background: linear-gradient(135deg, #e0e0e0, #bdbdbd); */
  color: #fff !important;
  opacity: 0.8;
}

/* 底部操作区域 */
.block-footer {
  padding: 15rpx 30rpx 30rpx;
  display: flex;
  justify-content: center;
  width: 94%;
  box-sizing: border-box;
  position: absolute; /* 使用绝对定位 */
  bottom: 0; /* 固定在底部 */
  left: 3%;
  z-index: 2; /* 确保在其他元素上方 */
}

.footer-actions {
  display: flex;
  justify-content: center; /* 水平居中 */
  width: 100%;
  margin: 0 auto;
  flex-wrap: wrap; /* 允许按钮换行 */
  gap: 10rpx; /* 按钮之间的间距 */
}

.action-btn {
  flex: 1;
  min-width: 45%; /* 确保一行最多显示两个按钮 */
  height: 70rpx; /* 减小按钮高度 */
  line-height: 70rpx;
  color: #2979ff;
  border-radius: 35rpx;
  font-size: 26rpx;
  border: 1px solid rgba(41,121,255,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8); /* 添加半透明背景 */
  margin: 5rpx;
}

/* 授权管理按钮样式 */
.action-btn.auth-btn {
  background-color: rgba(255, 152, 0, 0.1); /* 浅橙色背景 */
  border-color: rgba(255, 152, 0, 0.3);
  color: #FF9800;
}

/* 扫码开锁按钮样式 */
.action-btn.scan-btn {
  background-color: rgba(76, 175, 80, 0.1); /* 浅绿色背景 */
  border-color: rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.action-btn text {
  margin-right: 4rpx;
}

.group-btn {
  background-color: rgba(41,121,255,0.1);
}

.detail-btn {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: rgba(255,255,255,0.6);
  color: #2979ff;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: 1px solid rgba(41,121,255,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-btn text {
  margin-right: 6rpx;
}

.pagination-dots {
  position: absolute;
  bottom: 20rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.pagination-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: rgba(255,255,255,0.5);
  margin: 0 10rpx;
  transition: all 0.3s ease;
}

.pagination-dot.active {
  width: 30rpx;
  border-radius: 10rpx;
  background-color: #fff;
  box-shadow: 0 0 10rpx rgba(255,255,255,0.8);
}

/* 详情抽屉 */
.detail-drawer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  pointer-events: none;
}

.drawer-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.drawer-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 75vh;
  background-color: #fff;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  transform: translateY(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  pointer-events: none;
}

.drawer-show {
  pointer-events: auto;
}

.drawer-show .drawer-mask {
  opacity: 1;
  pointer-events: auto;
}

.drawer-show .drawer-content {
  transform: translateY(0);
  pointer-events: auto;
}

.drawer-header {
  padding: 20rpx; /* 减少内边距 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.drawer-title {
  font-size: 34rpx;
  font-weight: bold;
}

.drawer-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.close-icon {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.drawer-close:active {
  transform: scale(0.95);
  background-color: #e5e5e5;
}

.drawer-body {
  flex: 1;
  padding: 20rpx; /* 减少内边距 */
  overflow-y: auto;
  width: initial;
}

.detail-section {
  margin-bottom: 25rpx; /* 减少间距 */
}

.detail-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.detail-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background-color: #2979ff;
  border-radius: 4rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.detail-label {
  color: #666;
  font-size: 28rpx;
}

.detail-value {
  font-size: 28rpx;
  font-weight: 500;
}

.text-success {
  color: var(--success-color);
}

.text-primary {
  color: #2979ff;
}

.text-error {
  color: var(--error-color);
}

.text-muted {
  color: #999;
}

.operation-list {
  margin-top: 20rpx;
}

.operation-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.operation-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.operation-icon.success {
  background-color: var(--success-color);
}

.operation-icon.error {
  background-color: var(--error-color);
}

.operation-icon.warning {
  background-color: #ff9800;
}

.operation-info {
  flex: 1;
}

.operation-title {
  font-size: 28rpx;
  margin-bottom: 5rpx;
}

.operation-time {
  font-size: 24rpx;
  color: #999;
}

.drawer-footer {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  border-top: 1rpx solid #f0f0f0;
}

.drawer-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drawer-btn .btn-icon {
  margin-right: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drawer-btn.secondary {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 0; /* 移除右边距 */
  width: 50%; /* 设置宽度为50% */
  max-width: 300rpx; /* 设置最大宽度 */
}

.drawer-btn.primary {
  background: linear-gradient(135deg, #2979ff, #1565c0);
  color: #fff;
}

.drawer-btn.primary.disabled {
  background: linear-gradient(135deg, #f44336, #c62828);
  color: #fff;
  opacity: 0.9;
  box-shadow: 0 0 20rpx rgba(244, 67, 54, 0.7);
}

/* 设备组详情样式 */
.group-summary {
  display: flex;
  align-items: center;
  margin: 20rpx 0 30rpx;
}

.group-badge {
  background: linear-gradient(135deg, #2979ff, #1565c0);
  color: #fff;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  padding: 15rpx 40rpx;
  margin-right: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(41,121,255,0.3);
}

.group-count {
  font-size: 40rpx;
  font-weight: bold;
  margin-right: 8rpx;
}

.group-unit {
  font-size: 26rpx;
}

.group-status {
  flex: 1;
}

.group-status-item {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  background-color: #f5f5f5;
  font-size: 26rpx;
  color: #999;
  display: inline-flex;
  margin-right: 20rpx;
}

.group-status-item uniIcons {
  margin-right: 6rpx;
}

.group-status-item.active {
  background-color: rgba(41,121,255,0.1);
  color: #2979ff;
}

.devices-list {
  margin-top: 20rpx;
}

.device-card {
  background-color: #f9f9f9;
  border-radius: 20rpx;
  margin-bottom: 15rpx; /* 减少间距 */
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.device-card-header {
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f0f0f0;
}

.device-card-name {
  font-size: 30rpx;
  font-weight: bold;
}

.device-card-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.device-card-status.online {
  background-color: rgba(25,190,107,0.1);
  color: var(--success-color);
  border: 1px solid rgba(25,190,107,0.2);
}

.device-card-status.offline {
  background-color: rgba(144,147,153,0.1);
  color: var(--info-color);
  border: 1px solid rgba(144,147,153,0.2);
}

.device-card-content {
  padding: 15rpx; /* 减少内边距 */
}

.device-card-info {
  margin-bottom: 20rpx;
}

.card-info-item {
  display: flex;
  margin-bottom: 10rpx;
}

.card-info-label {
  color: #666;
  font-size: 26rpx;
  margin-right: 20rpx;
  width: 140rpx;
}

.card-info-value {
  font-size: 26rpx;
  flex: 1;
}

.device-card-btn {
  width: 100%;
  height: 70rpx;
  line-height: 70rpx;
  border-radius: 35rpx;
  font-size: 26rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.device-card-btn .btn-icon {
  margin-right: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.device-card-btn.active {
  background: linear-gradient(135deg, #2979ff, #1565c0);
  color: #fff;
}

.device-card-btn.inactive {
  background: linear-gradient(135deg, #f44336, #c62828);
  color: #fff;
  box-shadow: 0 0 25rpx rgba(244, 67, 54, 0.8);
  /* 移除动画效果 */
  /* animation: pulseShadow 1.5s infinite; */
}

.device-card-btn.warning {
  background: linear-gradient(135deg, #f44336, #c62828);
  color: #fff;
  box-shadow: 0 0 25rpx rgba(244, 67, 54, 0.8);
  animation: pulseShadow 1.5s infinite;
}

.device-card-btn.disabled {
  background: linear-gradient(135deg, #f44336, #c62828); /* 改为红底 */
  color: #fff !important; /* 改为白字 */
  opacity: 0.85;
  box-shadow: 0 0 20rpx rgba(244, 67, 54, 0.6); /* 添加红色阴影效果 */
}

.batch-actions {
  display: flex;
  margin-top: 20rpx;
}

.batch-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  margin: 0 10rpx;
}

.batch-btn.active {
  background: linear-gradient(135deg, #2979ff, #1565c0);
  color: #fff;
}

.batch-btn.inactive {
  background: linear-gradient(135deg, #f44336, #c62828);
  color: #fff;
  box-shadow: 0 0 25rpx rgba(244, 67, 54, 0.8);
  /* 移除动画效果 */
  /* animation: pulseShadow 1.5s infinite; */
}

.batch-btn[disabled] {
  opacity: 0.5;
}

.alarm-explanation-container {
  height: 60rpx; /* 固定高度 */
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
  position: absolute;
  bottom: 80rpx;
}

.alarm-explanation {
  font-size: 24rpx;
  color: #f44336;
  text-align: center;
  font-weight: 500;
  display: inline-block;
  background-color: rgba(244, 67, 54, 0.08);
  padding: 8rpx 20rpx;
  border-radius: 6rpx;
}

/* 模态弹窗 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  width: 80%;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
  z-index: 1001;
}

.modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 34rpx;
  font-weight: bold;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.modal-btn.secondary {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 20rpx;
}

.modal-btn.primary {
  background: linear-gradient(135deg, #2979ff, #1565c0);
  color: #fff;
}

/* 表单样式 */
.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  display: block;
}

.form-input {
  height: 80rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.form-picker {
  width: 100%;
  height: 80rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
}

.picker-value {
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

/* 设备选择列表 */
.device-checklist {
  border: 1px solid #ddd;
  border-radius: 8rpx;
  max-height: 300rpx;
  overflow-y: auto;
}

.device-check-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}

.device-check-item:last-child {
  border-bottom: none;
}

.device-check-item.selected {
  background-color: rgba(41,121,255,0.05);
}

.device-check-name {
  font-size: 28rpx;
}

.device-check-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 操作菜单 */
.action-sheet {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.action-sheet-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.action-sheet-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  overflow: hidden;
}

.action-sheet-title {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.action-sheet-item {
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-sheet-item uniIcons {
  margin-right: 10rpx;
}

.action-sheet-cancel {
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 32rpx;
  color: #999;
  background-color: #f8f8f8;
  margin-top: 10rpx;
}

/* 组列表 */
.group-list {
  border: 1px solid #ddd;
  border-radius: 8rpx;
}

.group-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 20rpx;
  border-bottom: 1px solid #f0f0f0;
}

.group-list-item:last-child {
  border-bottom: none;
}

.group-list-name {
  font-size: 30rpx;
  font-weight: 500;
}

.group-list-count {
  font-size: 26rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 6rpx 15rpx;
  border-radius: 20rpx;
}

/* 电量条样式 */
.battery-bar {
  width: 120rpx;
  height: 20rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
  display: inline-block;
  vertical-align: middle;
  margin-right: 10rpx;
}

.battery-fill {
  height: 100%;
  border-radius: 10rpx;
  transition: all 0.3s ease;
}

.battery-text {
  font-size: 24rpx;
  font-weight: 500;
  vertical-align: middle;
}

.battery-bar-detail {
  width: 200rpx;
  height: 24rpx;
  background-color: #f0f0f0;
  border-radius: 12rpx;
  overflow: hidden;
  display: inline-block;
  vertical-align: middle;
}

.battery-bar-card {
  width: 90rpx;
  height: 16rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
  display: inline-block;
  vertical-align: middle;
}

.detail-value {
  display: flex;
  align-items: center;
}

.card-info-value {
  display: flex;
  align-items: center;
}

/* 电量低警告动画 */
@keyframes batteryLowPulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.battery-low {
  animation: batteryLowPulse 2s infinite;
}

.drawer-btn-container {
  display: flex;
  justify-content: center; /* 改为居中显示 */
  width: 100%;
}

.drawer-alarm-container {
  margin-top: 15rpx;
  padding-top: 10rpx;
  border-top: 1rpx solid #f0f0f0;
}

.group-battery {
  display: flex;
  align-items: center;
  margin-top: 15rpx;
}

.group-battery-details {
  margin-top: 15rpx;
}

.group-avg-battery {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.group-avg-label {
  font-size: 26rpx;
  margin-right: 10rpx;
  color: #666;
  width: 80rpx;
}

.group-battery-stats {
  display: flex;
  flex-direction: column;
  margin-top: 10rpx;
  margin-bottom: 15rpx;
  background-color: rgba(245, 245, 245, 0.6);
  padding: 15rpx;
  border-radius: 10rpx;
}

.battery-stat-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 16rpx;
  background-color: #f9f9f9;
  padding: 16rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.03);
  border-left: 4rpx solid transparent;
  position: relative;
  overflow: hidden;
}

/* 根据电量自动设置左侧边框颜色 */
.battery-stat-item.high-battery {
  border-left-color: #4CD964;
}

.battery-stat-item.medium-battery {
  border-left-color: #FF9500;
}

.battery-stat-item.low-battery {
  border-left-color: #FF3B30;
}

/* 添加设备组内设备高亮效果 */
.group-device-item.high-battery {
  border-left-color: #4CD964;
}

.group-device-item.medium-battery {
  border-left-color: #FF9500;
}

.group-device-item.low-battery {
  border-left-color: #FF3B30;
}

.battery-device-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  width: 100%;
}

.battery-info {
  display: flex;
  align-items: center;
  width: 100%;
}

.battery-device-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 60%;
}

.battery-device-status {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
}

.status-online {
  background-color: rgba(25,190,107,0.1);
  color: var(--success-color);
}

.status-offline {
  background-color: rgba(144,147,153,0.1);
  color: var(--info-color);
}

.battery-icon-warning {
  margin-left: 6rpx;
  animation: batteryLowPulse 2s infinite;
}

/* 在小屏幕设备上调整电量条显示 */
@media screen and (max-width: 375px) {
  .battery-bar-mini {
    width: 40rpx;
  }
  
  .battery-text-mini {
    font-size: 18rpx;
  }
  
  .battery-device-name {
    width: 130rpx;
  }
}

.battery-bar-detail {
  min-width: 140rpx;
  flex-grow: 1;
  max-width: 70%;
}

.show-more-btn {
  width: 100%;
  text-align: center;
  font-size: 24rpx;
  color: #2979ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15rpx;
  padding: 12rpx 0;
  background-color: rgba(41, 121, 255, 0.05);
  border-radius: 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(41, 121, 255, 0.1);
}

.show-more-btn uniIcons {
  margin-left: 6rpx;
}

.filter-controls {
  display: flex;
  gap: 10rpx;
}

.filter-btn {
  padding: 5rpx 15rpx;
  border-radius: 15rpx;
  font-size: 22rpx;
  border: 1px solid #ddd;
  background-color: #f5f5f5;
  color: #666;
}

.filter-btn.active {
  background-color: #2979ff;
  color: #fff;
  border-color: #2979ff;
}

.drawer-btn.primary.disabled {
  background: linear-gradient(135deg, #f44336, #c62828);
  color: #fff;
  opacity: 0.9;
  box-shadow: 0 0 20rpx rgba(244, 67, 54, 0.7);
}

.card-info-value.alarm-explanation {
  display: flex;
  align-items: center;
}

.card-info-value.alarm-explanation text {
  font-size: 24rpx;
}

.group-name-container {
  display: flex;
  align-items: center;
  max-width: 70%;
}

.group-name {
  display: flex;
  align-items: center;
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
  transition: all 0.3s ease;
}

.group-name:active {
  background-color: #f0f0f0;
}

.edit-icon {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.group-name-edit {
  display: flex;
  align-items: center;
}

.group-name-input {
  width: 300rpx;
  height: 60rpx;
  border: 1px solid #2979ff;
  border-radius: 6rpx;
  padding: 0 10rpx;
  font-size: 28rpx;
  margin-right: 10rpx;
}

.save-name-btn {
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 20rpx;
  font-size: 24rpx;
  background-color: #2979ff;
  color: #fff;
  border-radius: 6rpx;
  margin-right: 10rpx;
}

.cancel-name-btn {
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 20rpx;
  font-size: 24rpx;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 6rpx;
  border: 1px solid #ddd;
}

.group-device-count {
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 15rpx;
  border-radius: 15rpx;
}

/* 设备组名称编辑区域样式 */
.group-name-section {
  background: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.group-name-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx;
  border-radius: 8rpx;
  transition: all 0.2s ease;
}

.group-name-display:active {
  background-color: rgba(0,0,0,0.03);
}

.group-name-text {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.group-name-edit-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.edit-icon {
  font-size: 30rpx;
  color: #666;
}

.group-name-edit-area {
  margin: 10rpx 0;
}

.group-name-input-large {
  width: 100%;
  height: 80rpx;
  border: 1px solid #2979ff;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  margin-bottom: 20rpx;
  background: #fff;
}

.group-name-buttons {
  display: flex;
  justify-content: flex-end;
}

.group-name-btn {
  height: 70rpx;
  line-height: 70rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
  margin-left: 20rpx;
}

.group-name-btn.save {
  background-color: #2979ff;
  color: #fff;
}

.group-name-btn.cancel {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.group-device-count-badge {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #999;
}

/* 扫码开锁按钮样式 */
.scan-unlock-btn {
  margin-top: 60rpx;
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
  color: #FFFFFF;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 15rpx rgba(76, 175, 80, 0.3);
  border: none;
  transition: all 0.3s ease;
}

.scan-unlock-btn:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 10rpx rgba(76, 175, 80, 0.2);
}

.scan-unlock-btn uniIcons {
  margin-right: 10rpx;
}

/* 移除之前的授权管理按钮的相关样式 */
.action-btn.auth-btn {
  display: none;
}

/* 添加状态标签样式 */
.status-tag {
  display: inline-block;
  padding: 2rpx 10rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
  margin-left: 10rpx;
}

.status-tag.normal {
  background-color: rgba(76, 217, 100, 0.1);
  color: #4CD964;
}

.status-tag.warning {
  background-color: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}
</style> 