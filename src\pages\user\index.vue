<template>
  <view class="container user-container">
    <view class="user-header">
      <view class="user-info">
        <view class="avatar" @click="updateAvatar">
          <image :src="userInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
        </view>
        <view class="info">
          <view class="nickname">{{userInfo.nickname || '点击登录'}}</view>
          <view class="user-id" v-if="userInfo.userId">ID: {{userInfo.userId}}</view>
        </view>
      </view>
      <view class="setting-btn" @click="navigateTo('/pages/user/settings')">
        <uni-icons type="gear" size="24"></uni-icons>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">我的订单</view>
      <view class="order-types">
        <view class="type-item" @click="viewOrders('all')">
          <view class="type-icon">
            <uni-icons type="list" size="24" color="#2979ff"></uni-icons>
          </view>
          <view class="type-text">全部</view>
        </view>
        <view class="type-item" @click="viewOrders('pending')">
          <view class="type-icon">
            <uni-icons type="wallet" size="24" color="#ff9900"></uni-icons>
          </view>
          <view class="type-text">待付款</view>
          <view class="badge" v-if="orderCounts.pending > 0">{{orderCounts.pending}}</view>
        </view>
        <view class="type-item" @click="viewOrders('processing')">
          <view class="type-icon">
            <uni-icons type="car" size="24" color="#19be6b"></uni-icons>
          </view>
          <view class="type-text">待配送</view>
          <view class="badge" v-if="orderCounts.processing > 0">{{orderCounts.processing}}</view>
        </view>
        <view class="type-item" @click="viewOrders('completed')">
          <view class="type-icon">
            <uni-icons type="checkbox-filled" size="24" color="#909399"></uni-icons>
          </view>
          <view class="type-text">已完成</view>
        </view>
      </view>
    </view>
    
    <view class="menu-list">
      <view class="menu-item" @click="navigateTo('/pages/user/emergency')">
        <view class="menu-icon emergency">
          <uni-icons type="help" size="24" color="#fa3534"></uni-icons>
        </view>
        <view class="menu-content">
          <view class="menu-title">应急中心</view>
          <view class="menu-desc">紧急情况处理、安全指南</view>
        </view>
        <view class="menu-arrow">
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
      </view>
      
      <view class="menu-item" @click="navigateTo('/pages/order/history')">
        <view class="menu-icon history">
          <uni-icons type="calendar" size="24" color="#2979ff"></uni-icons>
        </view>
        <view class="menu-content">
          <view class="menu-title">历史订单</view>
          <view class="menu-desc">查看所有订气、商城订单</view>
        </view>
        <view class="menu-arrow">
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
      </view>
      
      <view class="menu-item" @click="navigateTo('/pages/user/inventory')">
        <view class="menu-icon inventory">
          <uni-icons type="paperplane" size="24" color="#19be6b"></uni-icons>
        </view>
        <view class="menu-content">
          <view class="menu-title">台帐管理</view>
          <view class="menu-desc">气瓶、报警器管理</view>
        </view>
        <view class="menu-arrow">
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
      </view>
      
      <view class="menu-item" @click="scanReturnBottle">
        <view class="menu-icon return-bottle">
          <uni-icons type="scan" size="24" color="#9c27b0"></uni-icons>
        </view>
        <view class="menu-content">
          <view class="menu-title">扫码退瓶</view>
          <view class="menu-desc">扫码归还气瓶/报警器</view>
        </view>
        <view class="menu-arrow">
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
      </view>
    </view>
    
    <view class="menu-list">
      <view class="menu-item" @click="contactService">
        <view class="menu-icon service">
          <uni-icons type="headphones" size="24" color="#ff9900"></uni-icons>
        </view>
        <view class="menu-content">
          <view class="menu-title">客服中心</view>
        </view>
        <view class="menu-arrow">
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
      </view>
      
      <view class="menu-item" @click="navigateTo('/pages/user/messages')">
        <view class="menu-icon message">
          <uni-icons type="notification" size="24" color="#909399"></uni-icons>
        </view>
        <view class="menu-content">
          <view class="menu-title">消息通知</view>
          <view class="badge message-badge" v-if="unreadMessages > 0">{{unreadMessages}}</view>
        </view>
        <view class="menu-arrow">
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
      </view>
      
      <view class="menu-item" @click="navigateTo('/pages/about')">
        <view class="menu-icon about">
          <uni-icons type="info" size="24" color="#2979ff"></uni-icons>
        </view>
        <view class="menu-content">
          <view class="menu-title">关于我们</view>
        </view>
        <view class="menu-arrow">
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {
        avatar: '',
        nickname: '张三',
        userId: '12345678'
      },
      orderCounts: {
        pending: 2,
        processing: 1
      },
      unreadMessages: 3
    }
  },
  
  onLoad() {
    this.getUserInfo()
  },
  
  methods: {
    // 获取用户信息
    getUserInfo() {
      // 实际应用中应调用API获取用户信息
      // 这里模拟数据，真实应用应该调用微信登录接口
      setTimeout(() => {
        // 模拟数据
        this.userInfo = {
          avatar: '/static/images/avatar.png', // 实际应用中使用微信头像
          nickname: '张三',
          userId: '12345678'
        }
      }, 500)
    },
    
    // 更新头像
    updateAvatar() {
      // 已登录则更新头像，未登录则调用登录
      if (!this.userInfo.userId) {
        this.login()
        return
      }
      
      uni.showToast({
        title: '更新头像功能开发中',
        icon: 'none'
      })
    },
    
    // 登录
    login() {
      uni.showToast({
        title: '登录功能开发中',
        icon: 'none'
      })
    },
    
    // 页面跳转
    navigateTo(url) {
      uni.navigateTo({ url })
    },
    
    // 查看订单
    viewOrders(type) {
      uni.navigateTo({
        url: `/pages/order/history?type=${type}`
      })
    },
    
    // 联系客服
    contactService() {
      uni.makePhoneCall({
        phoneNumber: '10086', // 实际应用中替换为真实客服电话
        success: () => {
          console.log('拨打电话成功')
        },
        fail: () => {
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          })
        }
      })
    },
    
    // 扫码退瓶
    scanReturnBottle() {
      // 开发测试阶段，提供选择模拟数据的选项
      uni.showActionSheet({
        itemList: ['实际扫码', '模拟气瓶数据', '模拟报警器数据', '模拟无效二维码'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              // 实际扫码
              uni.scanCode({
                success: (scanRes) => {
                  uni.navigateTo({
                    url: `/pages/user/bottle-return?code=${encodeURIComponent(scanRes.result)}`
                  });
                },
                fail: () => {
                  uni.showToast({
                    title: '扫码失败',
                    icon: 'none'
                  });
                }
              });
              break;
            case 1:
              // 模拟气瓶数据
              uni.navigateTo({
                url: `/pages/user/bottle-return?code=${encodeURIComponent('CYL12345678')}`
              });
              break;
            case 2:
              // 模拟报警器数据
              uni.navigateTo({
                url: `/pages/user/bottle-return?code=${encodeURIComponent('ALM87654321')}`
              });
              break;
            case 3:
              // 模拟无效二维码
              uni.navigateTo({
                url: `/pages/user/bottle-return?code=${encodeURIComponent('INVALID12345')}`
              });
              break;
          }
        }
      });
    }
  }
}
</script>

<style scoped>
.user-container {
  padding: 0;
}

.user-header {
  height: 260rpx;
  background: linear-gradient(to right, #3a8eff, #44b3ff);
  padding: 30rpx;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.avatar image {
  width: 100%;
  height: 100%;
}

.info .nickname {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.info .user-id {
  font-size: 24rpx;
  opacity: 0.8;
}

.setting-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

/* 订单区块 */
.section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.order-types {
  display: flex;
  justify-content: space-between;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.type-icon {
  width: 100rpx;
  height: 100rpx;
  background-color: #f8f8f8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
}

.type-text {
  font-size: 26rpx;
  color: #333;
}

.badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background-color: #fa3534;
  color: #fff;
  font-size: 20rpx;
  border-radius: 20rpx;
  min-width: 34rpx;
  height: 34rpx;
  line-height: 34rpx;
  text-align: center;
  padding: 0 6rpx;
}

/* 菜单列表 */
.menu-list {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.emergency {
  background-color: rgba(250,53,52,0.1);
}

.history {
  background-color: rgba(41,121,255,0.1);
}

.inventory {
  background-color: rgba(25,190,107,0.1);
}

.service {
  background-color: rgba(255,153,0,0.1);
}

.message {
  background-color: rgba(144,147,153,0.1);
}

.about {
  background-color: rgba(41,121,255,0.1);
}

.menu-content {
  flex: 1;
  position: relative;
}

.menu-title {
  font-size: 30rpx;
}

.menu-desc {
  font-size: 24rpx;
  color: #999;
  margin-top: 5rpx;
}

.menu-arrow {
  margin-left: 20rpx;
}

.message-badge {
  position: absolute;
  top: 0;
  right: 20rpx;
}

.return-bottle {
  background-color: rgba(156, 39, 176, 0.1);
}
</style> 