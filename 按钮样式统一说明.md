# 按钮样式统一说明

## 修改概述

将安全核查页面的底部固定返回按钮样式修改为与 `/pages/store/add-store` 页面的"下一步"按钮保持一致。

## 样式对比

### 修改前
```vue
<!-- 原来的按钮结构 -->
<view class="fixed-back-button" @click="backToList">
  <view class="back-button-content">
    <text class="back-icon">←</text>
    <text class="back-text">返回列表</text>
  </view>
</view>
```

### 修改后
```vue
<!-- 新的按钮结构 - 与 add-store 保持一致 -->
<view class="actions">
  <button class="action-btn uni-button uni-button-primary" @click="backToList">
    <text class="back-icon">←</text>
    <text class="back-text">返回列表</text>
  </button>
</view>
```

## 关键样式统一

### 1. 底部固定区域样式
```css
.actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 30rpx 30rpx;
  /* 兼容安卓和iOS的安全区域 */
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  padding-bottom: calc(30rpx + constant(safe-area-inset-bottom)); /* iOS 11.0-11.2 兼容 */
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20rpx;
  z-index: 1000;
}
```

### 2. 按钮基础样式
```css
.uni-button {
  min-width: 200rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  border: none;
  overflow: hidden;
}
```

### 3. 主要按钮样式
```css
.uni-button-primary {
  color: #fff;
  background: linear-gradient(135deg, #42b0ff, var(--primary-color));
}
```

### 4. 内容区域适配
```css
.content {
  /* 底部留出按钮区域的空间，避免内容被遮挡 */
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  padding-bottom: calc(120rpx + constant(safe-area-inset-bottom)); /* iOS 11.0-11.2 兼容 */
}
```

## 统一的设计特点

### 1. 视觉一致性
- **相同的按钮高度**：88rpx
- **相同的圆角半径**：44rpx
- **相同的渐变背景**：`linear-gradient(135deg, #42b0ff, var(--primary-color))`
- **相同的阴影效果**：`0 4rpx 10rpx rgba(0, 0, 0, 0.1)`

### 2. 交互一致性
- **相同的点击效果**：`transform: translateY(2rpx)`
- **相同的过渡动画**：`transition: all 0.3s ease`
- **相同的字体大小**：28rpx
- **相同的字体粗细**：500

### 3. 布局一致性
- **相同的底部固定布局**：`position: fixed; bottom: 0`
- **相同的安全区域适配**：支持 iPhone X 等设备
- **相同的内边距设置**：30rpx
- **相同的阴影效果**：`0 -2rpx 12rpx rgba(0, 0, 0, 0.1)`

## 兼容性保证

### 1. 安全区域适配
```css
/* 支持新旧语法，确保在不同iOS版本下都能正常显示 */
padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
padding-bottom: calc(30rpx + constant(safe-area-inset-bottom)); /* iOS 11.0-11.2 兼容 */
```

### 2. 小程序兼容
- 使用标准的 `button` 元素
- 避免使用小程序不支持的CSS属性
- 确保点击区域足够大（88rpx高度）

### 3. 响应式设计
```css
@media (max-width: 600rpx) {
  .uni-button {
    height: 80rpx;
    line-height: 80rpx;
    font-size: 26rpx;
  }
}
```

## 优势对比

### 修改前的问题
1. **样式不统一**：与项目其他页面按钮样式不一致
2. **视觉差异**：颜色、大小、圆角等与设计规范不符
3. **维护困难**：独立的样式代码，难以统一管理

### 修改后的优势
1. **样式统一**：与 add-store 页面完全一致的按钮样式
2. **设计规范**：符合项目整体的设计语言
3. **易于维护**：复用现有的样式代码，便于统一修改
4. **用户体验**：一致的交互体验，降低用户学习成本

## 测试建议

### 1. 功能测试
- [ ] 按钮点击功能正常
- [ ] 返回列表功能正常
- [ ] 页面切换流畅

### 2. 样式测试
- [ ] 按钮样式与 add-store 页面一致
- [ ] 在不同屏幕尺寸下显示正常
- [ ] 安全区域适配正确

### 3. 兼容性测试
- [ ] H5端显示正常
- [ ] 微信小程序显示正常
- [ ] 其他小程序平台显示正常
- [ ] 不同设备型号兼容性良好

## 总结

通过参考 `/pages/store/add-store` 页面的按钮样式，成功实现了：

1. **视觉统一**：按钮外观与项目其他页面保持一致
2. **代码复用**：使用相同的样式类和CSS变量
3. **体验优化**：提供一致的用户交互体验
4. **维护简化**：便于后续的样式统一管理和修改

这种统一的设计方法有助于提升整个应用的用户体验和代码质量。
