<template>
  <view class="container">
    <view class="emergency-header">
      <view class="header-title">应急中心</view>
      <view class="header-desc">燃气安全，生命至上</view>
    </view>
    
    <view class="alarm-panel">
      <button class="sos-btn" @click="triggerAlarm">一键报警</button>
      <view class="alarm-desc">紧急情况下点击上方按钮，系统将通知紧急联系人并共享您的位置</view>
    </view>
    
    <view class="alarm-panel">
      <button class="repair-btn" @click="callRepair">一键报修</button>
      <view class="alarm-desc">点击一键报修，我们将安排专业人员上门服务</view>
    </view>
    
    <view class="emergency-services">
      <view class="service-item" @click="callEmergency('110')">
        <view class="service-icon police">
          <uni-icons type="phone-filled" size="24" color="#fff"></uni-icons>
        </view>
        <view class="service-info">
          <view class="service-title">紧急报警</view>
          <view class="service-number">110</view>
        </view>
      </view>
      
      <view class="service-item" @click="callEmergency('119')">
        <view class="service-icon fire">
          <uni-icons type="phone-filled" size="24" color="#fff"></uni-icons>
        </view>
        <view class="service-info">
          <view class="service-title">消防救援</view>
          <view class="service-number">119</view>
        </view>
      </view>
      
      <view class="service-item" @click="callEmergency('120')">
        <view class="service-icon medical">
          <uni-icons type="phone-filled" size="24" color="#fff"></uni-icons>
        </view>
        <view class="service-info">
          <view class="service-title">医疗急救</view>
          <view class="service-number">120</view>
        </view>
      </view>
    </view>
    
    <view class="section guide-section">
      <view class="section-title">应急指南</view>
      <view class="guide-list">
        <view class="guide-item" v-for="(guide, index) in guides" :key="index" @click="showGuide(guide)">
          <view class="guide-icon">
            <image :src="guide.icon" mode="aspectFit"></image>
          </view>
          <view class="guide-content">
            <view class="guide-title">{{guide.title}}</view>
            <view class="guide-desc">{{guide.desc}}</view>
          </view>
          <view class="guide-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>
      </view>
    </view>
    
    <view class="section services-section">
      <view class="section-title">在线服务</view>
      <view class="service-grid">
        <view class="grid-item" @click="contactCustomer">
          <view class="grid-icon">
            <uni-icons type="chat" size="28" color="#2979ff"></uni-icons>
          </view>
          <view class="grid-text">在线咨询</view>
        </view>
        
        <view class="grid-item" @click="reportIssue">
          <view class="grid-icon">
            <uni-icons type="flag" size="28" color="#19be6b"></uni-icons>
          </view>
          <view class="grid-text">隐患上报</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      guides: [
        {
          id: 1,
          title: '燃气泄漏应急处理',
          desc: '如何正确处理家庭燃气泄漏情况',
          icon: '/static/images/emergency/gas-leak.png',
          content: '详细处理步骤...'
        },
        {
          id: 2,
          title: '燃气火灾应对',
          desc: '燃气火灾的应急自救措施',
          icon: '/static/images/emergency/fire.png',
          content: '详细处理步骤...'
        },
        {
          id: 3,
          title: '燃气中毒急救',
          desc: '燃气中毒的症状与应急处理方法',
          icon: '/static/images/emergency/first-aid.png',
          content: '详细处理步骤...'
        }
      ]
    }
  },
  
  methods: {
    // 触发报警
    triggerAlarm() {
      uni.showModal({
        title: '确认操作',
        content: '确定要触发紧急报警吗？系统将通知您的紧急联系人并共享您的位置信息',
        success: (res) => {
          if (res.confirm) {
            // 获取位置
            uni.getLocation({
              type: 'gcj02',
              success: (res) => {
                const latitude = res.latitude
                const longitude = res.longitude
                
                // 实际应用中这里应调用API发送报警信息和位置
                uni.showToast({
                  title: '报警信息已发送',
                  icon: 'success'
                })
                
                // 模拟报警后自动拨打紧急电话
                setTimeout(() => {
                  uni.showModal({
                    title: '拨打紧急电话',
                    content: '是否立即拨打紧急电话？',
                    success: (res) => {
                      if (res.confirm) {
                        this.callEmergency('110')
                      }
                    }
                  })
                }, 1500)
              },
              fail: () => {
                // 无法获取位置时也要发送报警
                uni.showToast({
                  title: '报警信息已发送，但无法获取位置',
                  icon: 'none'
                })
              }
            })
          }
        }
      })
    },
    
    // 拨打紧急电话
    callEmergency(number) {
      uni.makePhoneCall({
        phoneNumber: number,
        success: () => {
          console.log('拨打电话成功')
        },
        fail: () => {
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          })
        }
      })
    },
    
    // 查看应急指南详情
    showGuide(guide) {
      // 实际应用中可以跳转到详情页
      uni.showModal({
        title: guide.title,
        content: guide.content || '详细内容开发中...',
        showCancel: false
      })
    },
    
    // 在线客服咨询
    contactCustomer() {
      uni.showModal({
        title: '联系客服',
        content: '是否拨打客服热线？',
        success: (res) => {
          if (res.confirm) {
            uni.makePhoneCall({
              phoneNumber: '************',
              success: () => {
                console.log('拨打客服电话成功')
              },
              fail: () => {
                uni.showToast({
                  title: '拨打电话失败',
                  icon: 'none'
                })
              }
            })
          }
        }
      })
    },
    
    // 一键维修
    callRepair() {
      uni.showModal({
        title: '联系维修',
        content: '是否拨打维修服务热线？',
        success: (res) => {
          if (res.confirm) {
            uni.makePhoneCall({
              phoneNumber: '************',
              success: () => {
                console.log('拨打维修电话成功')
              },
              fail: () => {
                uni.showToast({
                  title: '拨打电话失败',
                  icon: 'none'
                })
              }
            })
          }
        }
      })
    },
    
    // 隐患上报
    reportIssue() {
      uni.navigateTo({
        url: '/pages/service/report'
      })
    }
  }
}
</script>

<style scoped>
.container {
  padding-bottom: 30rpx;
}
.emergency-header {
  height: 200rpx;
  background: linear-gradient(to right, #ff5050, #ff7676);
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 40rpx;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.header-desc {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 一键报警 */
.alarm-panel {
  padding: 0 40rpx;
  margin-bottom: 40rpx;
}

.sos-btn {
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  background-color: #fa3534;
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  border-radius: 50rpx;
  letter-spacing: 4rpx;
  box-shadow: 0 10rpx 20rpx rgba(250,53,52,0.3);
}

.repair-btn {
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  background-color: #2979ff;
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  border-radius: 50rpx;
  letter-spacing: 4rpx;
  box-shadow: 0 10rpx 20rpx rgba(41,121,255,0.3);
}

.alarm-desc {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
  padding: 0 40rpx;
}

/* 紧急服务 */
.emergency-services {
  display: flex;
  justify-content: space-around;
  padding: 0 20rpx;
  margin-bottom: 40rpx;
}

.service-item {
  display: flex;
  align-items: center;
  padding: 15rpx 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.05);
}

.service-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15rpx;
}

.police {
  background-color: #2979ff;
}

.fire {
  background-color: #fa3534;
}

.medical {
  background-color: #19be6b;
}

.service-title {
  font-size: 24rpx;
  margin-bottom: 5rpx;
}

.service-number {
  font-size: 28rpx;
  font-weight: bold;
}

/* 应急指南 */
.section {
  background-color: #fff;
  border-radius: 12rpx;
  margin: 0 20rpx 30rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 10rpx;
  height: 30rpx;
  width: 6rpx;
  background-color: var(--primary-color);
  border-radius: 3rpx;
}

.guide-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.guide-item:last-child {
  border-bottom: none;
}

.guide-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.guide-icon image {
  width: 100%;
  height: 100%;
}

.guide-content {
  flex: 1;
}

.guide-title {
  font-size: 30rpx;
  margin-bottom: 10rpx;
}

.guide-desc {
  font-size: 24rpx;
  color: #999;
}

/* 在线服务 */
.service-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  /* grid-template-rows: 1fr 1fr; */
  grid-gap: 30rpx;
}

.grid-item {
  height: 160rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.grid-icon {
  margin-bottom: 15rpx;
}

.grid-text {
  font-size: 28rpx;
}
</style>