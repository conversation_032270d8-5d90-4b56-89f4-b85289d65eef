<template>
  <view class="container mall-container">
    <view class="search-box">
      <view class="search-input">
        <uni-icons type="search" size="18" color="#999"></uni-icons>
        <input type="text" placeholder="搜索商品" confirm-type="search" @confirm="searchProducts" v-model="searchKeyword"/>
      </view>
      <view class="cart-btn" @click="navigateTo('/pages/mall/cart')">
        <uni-icons type="cart" size="24" color="#333"></uni-icons>
        <view class="cart-badge" v-if="cartCount > 0">{{cartCount}}</view>
      </view>
    </view>
    
    <scroll-view scroll-x class="category-scroll">
      <view class="category-list">
        <view 
          class="category-item" 
          :class="{active: currentCategory === index}" 
          v-for="(category, index) in categories" 
          :key="index"
          @click="changeCategory(index)"
        >
          {{category.name}}
        </view>
      </view>
    </scroll-view>
    
    <view class="sub-categories" v-if="subCategories.length > 0">
      <view 
        class="sub-category-item" 
        :class="{active: currentSubCategory === index}"
        v-for="(subCategory, index) in subCategories" 
        :key="index"
        @click="changeSubCategory(index)"
      >
        {{subCategory.name}}
      </view>
    </view>
    
    <view class="product-filter">
      <view 
        class="filter-item" 
        :class="{active: sortBy === 'default'}"
        @click="setSortBy('default')"
      >
        综合
      </view>
      <view 
        class="filter-item" 
        :class="{active: sortBy === 'sales'}"
        @click="setSortBy('sales')"
      >
        销量
      </view>
      <view class="filter-item" @click="setSortBy('price')">
        价格
        <view class="sort-icons">
          <uni-icons :type="sortBy === 'price' && sortOrder === 'asc' ? 'arrow-up-fill' : 'arrow-up'" size="12" color="#999"></uni-icons>
          <uni-icons :type="sortBy === 'price' && sortOrder === 'desc' ? 'arrow-down-fill' : 'arrow-down'" size="12" color="#999"></uni-icons>
        </view>
      </view>
      <view 
        class="filter-item" 
        :class="{active: viewType === 'grid'}"
        @click="toggleViewType"
      >
        <uni-icons :type="viewType === 'grid' ? 'grid-filled' : 'grid'" size="16" :color="viewType === 'grid' ? '#2979ff' : '#333'"></uni-icons>
      </view>
    </view>
    
    <view :class="['product-list', viewType]">
      <view 
        v-for="(product, index) in productList" 
        :key="index" 
        class="product-item"
        @click="viewProductDetail(product)"
      >
        <view class="product-image">
          <image :src="product.image" mode="aspectFill"></image>
        </view>
        <view class="product-info">
          <view class="product-name">{{product.name}}</view>
          <view class="product-desc" v-if="viewType === 'list'">{{product.description}}</view>
          <view class="product-price-row">
            <view class="product-price">¥{{product.price}}</view>
            <view class="product-sales" v-if="viewType === 'grid'">销量: {{product.sales}}</view>
            <view class="add-cart-btn" @click.stop="addToCart(product)">
              <uni-icons type="plusempty" size="20" color="#fff"></uni-icons>
            </view>
          </view>
          <view class="product-meta" v-if="viewType === 'list'">
            <view class="product-sales">销量: {{product.sales}}</view>
          </view>
        </view>
      </view>
    </view>
    
    <uni-load-more :status="loadMoreStatus" />
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchKeyword: '',
      categories: [
        { id: 0, name: '全部' },
        { id: 1, name: '气瓶配件' },
        { id: 2, name: '安全设备' },
        { id: 3, name: '厨房器具' },
        { id: 4, name: '维修工具' }
      ],
      currentCategory: 0,
      subCategories: [], // 子分类，根据当前分类动态加载
      currentSubCategory: 0,
      sortBy: 'default', // default, sales, price
      sortOrder: 'desc', // asc, desc
      viewType: 'grid', // list, grid
      productList: [],
      loadMoreStatus: 'more', // more, loading, noMore
      cartCount: 0,
      page: 1,
      limit: 10
    }
  },
  
  onLoad() {
    this.loadCategories()
    this.loadProducts()
  },
  
  onReachBottom() {
    if (this.loadMoreStatus !== 'noMore') {
      this.loadMoreProducts()
    }
  },
  
  methods: {
    // 加载分类
    loadCategories() {
      // 实际应用中应调用API获取分类
      // 这里为示例数据
      this.loadSubCategories()
    },
    
    // 加载子分类
    loadSubCategories() {
      // 根据当前选中的分类加载子分类
      const subCategoriesMap = {
        0: [],
        1: [
          { id: 10, name: '软管接头' },
          { id: 11, name: '阀门' },
          { id: 12, name: '压力表' }
        ],
        2: [
          { id: 20, name: '报警器' },
          { id: 21, name: '灭火器' },
          { id: 22, name: '感应器' }
        ],
        3: [
          { id: 30, name: '炉灶' },
          { id: 31, name: '热水器' }
        ],
        4: [
          { id: 40, name: '扳手' },
          { id: 41, name: '密封胶' },
          { id: 42, name: '检测仪' }
        ]
      }
      
      this.subCategories = subCategoriesMap[this.currentCategory] || []
      this.currentSubCategory = 0
      
      // 切换分类后重新加载商品
      this.page = 1
      this.productList = []
      this.loadProducts()
    },
    
    // 切换一级分类
    changeCategory(index) {
      if (this.currentCategory === index) return
      this.currentCategory = index
      this.loadSubCategories()
    },
    
    // 切换二级分类
    changeSubCategory(index) {
      if (this.currentSubCategory === index) return
      this.currentSubCategory = index
      
      // 切换子分类后重新加载商品
      this.page = 1
      this.productList = []
      this.loadProducts()
    },
    
    // 设置排序方式
    setSortBy(sort) {
      if (sort === this.sortBy) {
        // 如果点击当前排序方式，则切换排序顺序
        if (sort === 'price') {
          this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc'
        }
      } else {
        this.sortBy = sort
        if (sort === 'price') {
          this.sortOrder = 'asc' // 默认价格从低到高
        } else {
          this.sortOrder = 'desc'
        }
      }
      
      // 排序后重新加载商品
      this.page = 1
      this.productList = []
      this.loadProducts()
    },
    
    // 切换视图类型
    toggleViewType() {
      this.viewType = this.viewType === 'grid' ? 'list' : 'grid'
    },
    
    // 搜索商品
    searchProducts(e) {
      // 使用关键词搜索商品
      this.page = 1
      this.productList = []
      this.loadProducts()
    },
    
    // 加载商品列表
    loadProducts() {
      this.loadMoreStatus = 'loading'
      
      // 实际应用中应调用API获取商品列表
      // 这里模拟异步请求
      setTimeout(() => {
        // 模拟商品数据
        const newProducts = Array(10).fill().map((_, i) => ({
          id: this.productList.length + i + 1,
          name: `商品${this.productList.length + i + 1}`,
          description: '商品描述信息，这里是一些商品的详细说明',
          price: Math.floor(10 + Math.random() * 290),
          sales: Math.floor(10 + Math.random() * 1000),
          image: `/static/images/products/product-${1 + Math.floor(Math.random() * 6)}.png`
        }))
        
        this.productList = [...this.productList, ...newProducts]
        
        // 是否还有更多
        if (this.page >= 3) {
          this.loadMoreStatus = 'noMore'
        } else {
          this.loadMoreStatus = 'more'
        }
        
        this.page++
        
        // 模拟获取购物车数量
        this.cartCount = 5
      }, 1000)
    },
    
    // 加载更多商品
    loadMoreProducts() {
      if (this.loadMoreStatus === 'loading') return
      this.loadProducts()
    },
    
    // 查看商品详情
    viewProductDetail(product) {
      uni.navigateTo({
        url: `/pages/mall/detail?id=${product.id}`
      })
    },
    
    // 添加到购物车
    addToCart(product) {
      uni.showLoading({
        title: '添加中'
      })
      
      // 实际应用中应调用API添加到购物车
      // 这里模拟异步请求
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '添加成功',
          icon: 'success'
        })
        
        // 更新购物车数量
        this.cartCount++
      }, 500)
    },
    
    // 页面跳转
    navigateTo(url) {
      uni.navigateTo({ url })
    }
  }
}
</script>

<style scoped>
.mall-container {
  padding: 0;
}

.search-box {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.search-input {
  flex: 1;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}

.search-input input {
  flex: 1;
  height: 70rpx;
  padding-left: 15rpx;
  font-size: 28rpx;
}

.cart-btn {
  width: 80rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.cart-badge {
  position: absolute;
  top: 0;
  right: 10rpx;
  background-color: #fa3534;
  color: #fff;
  font-size: 20rpx;
  border-radius: 18rpx;
  min-width: 36rpx;
  height: 36rpx;
  line-height: 36rpx;
  text-align: center;
  padding: 0 6rpx;
}

.category-scroll {
  white-space: nowrap;
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  position: fixed;
  top: 110rpx;
  left: 0;
  right: 0;
  z-index: 99;
}

.category-list {
  display: inline-block;
  padding: 0 20rpx;
}

.category-item {
  display: inline-block;
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  border-radius: 30rpx;
  background-color: #f5f5f5;
}

.category-item.active {
  background-color: var(--primary-color);
  color: #fff;
}

.sub-categories {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  background-color: #fff;
  margin-top: 170rpx;
}

.sub-category-item {
  padding: 10rpx 20rpx;
  margin-right: 20rpx;
  margin-bottom: 15rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
  border: 1rpx solid #ddd;
}

.sub-category-item.active {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background-color: rgba(41,121,255,0.05);
}

.product-filter {
  display: flex;
  align-items: center;
  height: 80rpx;
  background-color: #fff;
  padding: 0 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
  margin-top: 20rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  margin-right: 40rpx;
  font-size: 28rpx;
  position: relative;
}

.filter-item.active {
  color: var(--primary-color);
  font-weight: bold;
}

.sort-icons {
  display: flex;
  flex-direction: column;
  margin-left: 5rpx;
}

.product-list {
  padding: 20rpx;
  background-color: #f8f8f8;
}

.product-list.grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.product-list.grid .product-item {
  width: calc(50% - 10rpx);
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.product-list.list .product-item {
  display: flex;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.product-list.grid .product-image {
  width: 100%;
  height: 300rpx;
}

.product-list.list .product-image {
  width: 200rpx;
  height: 200rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.product-image image {
  width: 100%;
  height: 100%;
}

.product-list.grid .product-info {
  padding: 20rpx;
}

.product-list.list .product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.product-price {
  color: #fa3534;
  font-size: 32rpx;
  font-weight: bold;
}

.product-sales {
  font-size: 24rpx;
  color: #999;
}

.product-meta {
  margin-top: 10rpx;
  display: flex;
  justify-content: space-between;
}

.add-cart-btn {
  width: 50rpx;
  height: 50rpx;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 