<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备归还确认页面测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            background-color: #f8f8f8;
            padding-bottom: 80px; /* 为固定按钮留出空间 */
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 20px;
        }
        .card {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
        }
        .label {
            color: #666;
        }
        .value {
            font-weight: 500;
        }
        .highlight {
            color: #2979ff;
            font-weight: bold;
        }
        .device-type-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 15px;
            color: white;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .device-type-badge.cylinder {
            background-color: #2979ff;
        }
        .device-type-badge.alarm {
            background-color: #fa3534;
        }
        .deposit-info {
            width: 100%;
        }
        .deposit-row {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            font-size: 16px;
        }
        .deposit-row.refund-row {
            border-top: 1px solid #eee;
            margin-top: 10px;
            padding-top: 15px;
            font-weight: bold;
        }
        .deposit-label {
            color: #666;
        }
        .deposit-amount {
            font-weight: 500;
            color: #333;
        }
        .refund-amount {
            color: #19be6b;
            font-size: 18px;
        }
        .deposit-note {
            display: flex;
            align-items: center;
            margin-top: 15px;
            background-color: rgba(41, 121, 255, 0.1);
            padding: 12px;
            border-radius: 6px;
            font-size: 14px;
            color: #2979ff;
        }
        .fixed-bottom-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: #fff;
            padding: 15px 20px;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            z-index: 1000;
        }
        .test-buttons {
            margin-top: 20px;
        }
        .test-button, .action-button {
            background: #2979ff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            margin: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .action-button {
            width: 45%;
        }
        .action-button.cancel {
            background: #f5f5f5;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">设备归还确认</div>
        <div class="subtitle">测试押金显示和固定底部按钮</div>
        
        <div class="test-buttons">
            <button class="test-button" onclick="testCylinder()">测试气瓶（有押金）</button>
            <button class="test-button" onclick="testAlarm()">测试报警器（有押金）</button>
            <button class="test-button" onclick="testAlarmNoDeposit()">测试报警器（无押金）</button>
        </div>
        
        <div id="result"></div>
    </div>
    
    <!-- 固定底部按钮 -->
    <div class="fixed-bottom-buttons">
        <button class="action-button cancel">取消</button>
        <button class="action-button">确认归还</button>
    </div>

    <script>
        // 报警器用户类型数据
        const alarmUserTypes = [
            { name: '居民用户', value: 'resident' },
            { name: '移动用户', value: 'mobile' },
            { name: '店铺/企业用户', value: 'business-enterprise' }
        ];

        function getAlarmUserTypeName(value) {
            const userType = alarmUserTypes.find(type => type.value === value);
            return userType ? userType.name : '未知类型';
        }

        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        function testCylinder() {
            const deviceInfo = {
                type: 'cylinder',
                id: 'CYL001',
                modelName: '液化石油气钢瓶',
                specification: '15kg',
                startDate: '2024-06-01',
                usageDays: 45,
                deposit: 100,
                status: '正常'
            };

            displayResult(deviceInfo);
        }

        function testAlarm() {
            const deviceInfo = {
                type: 'alarm',
                id: 'ALM001',
                modelName: '家用燃气报警器',
                startDate: '2024-05-15',
                usageDays: 75,
                deposit: 50,
                status: '正常',
                userType: 'resident'
            };

            displayResult(deviceInfo);
        }

        function testAlarmNoDeposit() {
            const deviceInfo = {
                type: 'alarm',
                id: 'ALM002',
                modelName: '商用燃气报警器',
                startDate: '2024-04-10',
                usageDays: 110,
                deposit: 0,
                status: '正常',
                userType: 'business-enterprise'
            };

            displayResult(deviceInfo);
        }

        function displayResult(deviceInfo) {
            const resultDiv = document.getElementById('result');

            let html = `
                <div class="device-type-badge ${deviceInfo.type}">
                    ${deviceInfo.type === 'cylinder' ? '气瓶' : '报警器'}
                </div>

                <div class="card">
                    <div class="card-title">设备信息</div>
                    <div class="info-item">
                        <span class="label">设备编号</span>
                        <span class="value">${deviceInfo.id}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">设备类型</span>
                        <span class="value">${deviceInfo.modelName}</span>
                    </div>
                    ${deviceInfo.specification ? `
                    <div class="info-item">
                        <span class="label">规格</span>
                        <span class="value highlight">${deviceInfo.specification}</span>
                    </div>
                    ` : ''}
                    <div class="info-item">
                        <span class="label">状态</span>
                        <span class="value">${deviceInfo.status}</span>
                    </div>
                </div>

                <div class="card">
                    <div class="card-title">使用信息</div>
                    <div class="info-item">
                        <span class="label">开始日期</span>
                        <span class="value">${deviceInfo.startDate}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">已使用</span>
                        <span class="value highlight">${deviceInfo.usageDays}天</span>
                    </div>
                    ${deviceInfo.type === 'alarm' ? `
                    <div class="info-item">
                        <span class="label">用户类型</span>
                        <span class="value">${getAlarmUserTypeName(deviceInfo.userType)}</span>
                    </div>
                    ` : ''}
                    <div class="info-item">
                        <span class="label">今日归还</span>
                        <span class="value">${formatDate(new Date())}</span>
                    </div>
                </div>

                ${deviceInfo.deposit > 0 ? `
                <div class="card">
                    <div class="card-title">押金信息</div>
                    <div class="deposit-info">
                        <div class="deposit-row">
                            <span class="deposit-label">押金金额</span>
                            <span class="deposit-amount">¥${deviceInfo.deposit.toFixed(2)}</span>
                        </div>
                        <div class="deposit-row refund-row">
                            <span class="deposit-label">应退金额</span>
                            <span class="deposit-amount refund-amount">¥${deviceInfo.deposit.toFixed(2)}</span>
                        </div>
                    </div>
                    <div class="deposit-note">
                        ℹ️ 押金将在确认设备无损后的1-3个工作日内退回您的账户
                    </div>
                </div>
                ` : ''}
            `;

            resultDiv.innerHTML = html;
        }
    </script>
</body>
</html>
