<script>
export default {
  onLaunch: function () {
    console.log('App Launch')
    // 检查更新
    this.checkUpdate()
  },
  onShow: function () {
    console.log('App Show')
  },
  onHide: function () {
    console.log('App Hide')
  },
  methods: {
    checkUpdate() {
      // 微信小程序检查更新
      if (uni.canIUse('getUpdateManager')) {
        const updateManager = uni.getUpdateManager()
        updateManager.onCheckForUpdate(function (res) {
          if (res.hasUpdate) {
            updateManager.onUpdateReady(function () {
              uni.showModal({
                title: '更新提示',
                content: '新版本已经准备好，是否重启应用？',
                success: function (res) {
                  if (res.confirm) {
                    updateManager.applyUpdate()
                  }
                }
              })
            })
            updateManager.onUpdateFailed(function () {
              uni.showModal({
                title: '提示',
                content: '检查更新失败，请稍后再试'
              })
            })
          }
        })
      }
    }
  }
}
</script>

<style>
flex { 
  display: flex;
}
/* 全局样式 */
page {
  --primary-color: #4c6ef5;
  --success-color: #19be6b;
  --warning-color: #ff9900;
  --error-color: #fa3534;
  --info-color: #909399;
  --bg-color: #f8f8f8;
  background-color: var(--bg-color);
  color: #333;
  font-size: 28rpx;
}

.container {
  /* padding: 30rpx; */
}

.btn-primary {
  background-color: var(--primary-color);
  color: #ffffff;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  text-align: center;
}

.btn-disabled {
  background-color: #cccccc;
  color: #ffffff;
}

.card {
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}


uni-button:after {
  border: 0 !important;
}
</style>
