<template>
  <view class="container">
    <view class="device-info card">
      <view class="device-header">
        <view class="device-name">{{device.name}}</view>
        <view class="device-status" :class="{'status-online': device.isOnline, 'status-offline': !device.isOnline}">
          {{device.isOnline ? '在线' : '离线'}}
        </view>
      </view>
      
      <view class="info-grid">
        <view class="info-item">
          <view class="info-label">气瓶编码</view>
          <view class="info-value">{{device.code}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">购买日期</view>
          <view class="info-value">{{device.purchaseDate}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">报警器状态</view>
          <view class="info-value" :class="{'alarm-normal': device.alarmStatus === 'normal', 'alarm-warning': device.alarmStatus === 'warning'}">
            {{device.alarmStatus === 'normal' ? '正常' : '异常'}}
          </view>
        </view>
        <view class="info-item">
          <view class="info-label">剩余气量</view>
          <view class="info-value">{{device.gasRemaining}}%</view>
        </view>
      </view>
      
      <view class="control-panel">
        <view class="control-title">阀门控制</view>
        <view class="control-switch">
          <text>{{device.valveOpen ? '开' : '关'}}</text>
          <switch 
            :checked="device.valveOpen" 
            @change="toggleValve"
            :disabled="!device.isOnline"
          />
        </view>
      </view>
    </view>
    
    <view class="realtime-data card">
      <view class="section-title">实时数据</view>
      <view class="data-grid">
        <view class="data-item">
          <view class="data-icon pressure">
            <uni-icons type="arrow-down" size="24" color="#2979ff"></uni-icons>
          </view>
          <view class="data-info">
            <view class="data-value">{{device.pressure}} MPa</view>
            <view class="data-label">压力</view>
          </view>
        </view>
        
        <view class="data-item">
          <view class="data-icon temperature">
            <uni-icons type="fire" size="24" color="#ff9900"></uni-icons>
          </view>
          <view class="data-info">
            <view class="data-value">{{device.temperature}} °C</view>
            <view class="data-label">温度</view>
          </view>
        </view>
        
        <view class="data-item">
          <view class="data-icon gas">
            <uni-icons type="gear" size="24" color="#19be6b"></uni-icons>
          </view>
          <view class="data-info">
            <view class="data-value">{{device.gasRemaining}}%</view>
            <view class="data-label">剩余气量</view>
          </view>
        </view>
      </view>
    </view>
    
    <view class="data-chart card">
      <view class="section-title">历史数据</view>
      <view class="chart-tabs">
        <view 
          v-for="(tab, index) in chartTabs" 
          :key="index" 
          :class="['tab-item', {active: currentTab === index}]"
          @click="switchTab(index)"
        >
          {{tab}}
        </view>
      </view>
      <view class="chart-container">
        <image src="/static/images/chart-placeholder.png" mode="widthFix" class="chart-image"></image>
      </view>
    </view>
    
    <view class="alarm-history card">
      <view class="section-title">报警记录</view>
      <view class="alarm-list" v-if="alarmHistory.length > 0">
        <view class="alarm-item" v-for="(alarm, index) in alarmHistory" :key="index">
          <view class="alarm-icon" :class="alarm.level">
            <uni-icons type="error" size="16" color="#fff"></uni-icons>
          </view>
          <view class="alarm-content">
            <view class="alarm-message">{{alarm.message}}</view>
            <view class="alarm-time">{{alarm.time}}</view>
          </view>
        </view>
      </view>
      <view class="no-data" v-else>
        <image src="/static/images/no-alarm.png" mode="aspectFit"></image>
        <text>暂无报警记录</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      deviceId: null,
      device: {
        id: 0,
        name: '',
        code: '',
        purchaseDate: '',
        alarmStatus: 'normal',
        isOnline: true,
        valveOpen: false,
        pressure: 0,
        temperature: 0,
        gasRemaining: 0
      },
      chartTabs: ['压力', '温度', '气量'],
      currentTab: 0,
      alarmHistory: []
    }
  },
  
  onLoad(options) {
    if (options.id) {
      this.deviceId = options.id
      this.loadDeviceDetail()
    } else {
      uni.showToast({
        title: '设备ID错误',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  },
  
  methods: {
    // 加载设备详情
    loadDeviceDetail() {
      uni.showLoading({
        title: '加载中'
      })
      
      // 实际应用中应调用API获取设备详情
      // 这里模拟数据
      setTimeout(() => {
        this.device = {
          id: this.deviceId,
          name: '厨房主气瓶',
          code: 'LPG12345678',
          purchaseDate: '2023-09-15',
          alarmStatus: 'normal',
          isOnline: true,
          valveOpen: true,
          pressure: 0.48,
          temperature: 24,
          gasRemaining: 65
        }
        
        this.alarmHistory = [
          {
            id: 1,
            level: 'warning',
            message: '气瓶压力异常',
            time: '2023-12-10 14:30:25'
          },
          {
            id: 2,
            level: 'info',
            message: '气量不足20%',
            time: '2023-12-08 09:15:32'
          }
        ]
        
        uni.hideLoading()
      }, 1000)
    },
    
    // 切换阀门状态
    toggleValve() {
      if (!this.device.isOnline) {
        uni.showToast({
          title: '设备离线，无法控制',
          icon: 'none'
        })
        return
      }
      
      uni.showModal({
        title: '确认操作',
        content: `确定要${this.device.valveOpen ? '关闭' : '打开'}阀门吗？`,
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '操作中'
            })
            
            // 模拟API调用
            setTimeout(() => {
              // 更新本地状态
              this.device.valveOpen = !this.device.valveOpen
              
              uni.hideLoading()
              uni.showToast({
                title: `阀门已${this.device.valveOpen ? '打开' : '关闭'}`,
                icon: 'success'
              })
            }, 1000)
          }
        }
      })
    },
    
    // 切换图表选项卡
    switchTab(index) {
      this.currentTab = index
    }
  }
}
</script>

<style scoped>
.device-info {
  margin-bottom: 20rpx;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
  margin-bottom: 20rpx;
}

.device-name {
  font-size: 36rpx;
  font-weight: bold;
}

.device-status {
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}

.status-online {
  background-color: rgba(25,190,107,0.1);
  color: var(--success-color);
}

.status-offline {
  background-color: rgba(144,147,153,0.1);
  color: var(--info-color);
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 20rpx;
  padding: 10rpx 0;
}

.info-item {
  padding: 10rpx;
}

.info-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.info-value {
  font-size: 28rpx;
  font-weight: bold;
}

.alarm-normal {
  color: var(--success-color);
}

.alarm-warning {
  color: var(--error-color);
}

.control-panel {
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.control-title {
  font-size: 28rpx;
  font-weight: bold;
}

.control-switch {
  display: flex;
  align-items: center;
}

.control-switch text {
  margin-right: 20rpx;
  font-size: 28rpx;
}

/* 实时数据 */
.realtime-data {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.data-grid {
  display: flex;
  justify-content: space-around;
}

.data-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 160rpx;
}

.data-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
}

.pressure {
  background-color: rgba(41,121,255,0.1);
}

.temperature {
  background-color: rgba(255,153,0,0.1);
}

.gas {
  background-color: rgba(25,190,107,0.1);
}

.data-info {
  text-align: center;
}

.data-value {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.data-label {
  font-size: 24rpx;
  color: #666;
}

/* 图表 */
.data-chart {
  margin-bottom: 20rpx;
}

.chart-tabs {
  display: flex;
  margin-bottom: 20rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 15rpx 0;
  font-size: 28rpx;
  border-bottom: 2rpx solid transparent;
}

.tab-item.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.chart-container {
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-image {
  width: 100%;
  height: auto;
}

/* 报警记录 */
.alarm-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.alarm-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.alarm-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.alarm-icon.warning {
  background-color: var(--error-color);
}

.alarm-icon.info {
  background-color: var(--info-color);
}

.alarm-content {
  flex: 1;
}

.alarm-message {
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.alarm-time {
  font-size: 24rpx;
  color: #999;
}

.no-data {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
  font-size: 28rpx;
}

.no-data image {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}
</style> 