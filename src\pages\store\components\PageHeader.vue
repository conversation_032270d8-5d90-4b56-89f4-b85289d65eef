<template>
  <view class="page-header">
    <view class="header-background">
      <view class="bg-shape"></view>
    </view>
    
    <view class="header-content">
      <view class="navigation-area">
        <!-- <view class="back-button" @click="goBack">
          <view class="back-icon">
            <text class="back-arrow">←</text>
          </view>
        </view> -->
        
        <view class="title-container">
          <text class="title-text">{{ title }}</text>
          <!-- <view class="title-underline"></view> -->
        </view>
      </view>
      
      <view class="action-area">
        <slot name="actions"></slot>
      </view>
    </view>
  </view>
</template>

<script setup>
// 定义props
const props = defineProps({
  title: {
    type: String,
    required: true
  }
})

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    delta: 1,
    fail: () => {
      uni.switchTab({
        url: '/pages/index/index'
      })
    }
  })
}
</script>

<style scoped>
.page-header {
  position: relative;
  height: 160rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
}

/* Background with unique shape */
.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #FFFFFF;
  overflow: hidden;
  z-index: 1;
}

.bg-shape {
  position: absolute;
  width: 280%;
  height: 300rpx;
  background-color: var(--primary-color);
  border-radius: 0 0 50% 50%;
  left: -90%;
  top: -100rpx;
  box-shadow: 0 10rpx 30rpx rgba(74, 102, 183, 0.2);
  z-index: 1;
}

/* Content layout */
.header-content {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 30rpx 40rpx;
  padding-top: calc(30rpx + var(--status-bar-height, 0));
  z-index: 2;
}

.navigation-area {
  display: flex;
  align-items: center;
}

.back-button {
  flex-shrink: 0;
  margin-right: 20rpx;
}

.back-icon {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(6rpx);
  -webkit-backdrop-filter: blur(6rpx);
  transition: all 0.2s ease;
}

.back-arrow {
  color: white;
  font-size: 36rpx;
  font-weight: 300;
}

.back-icon:active {
  transform: scale(0.92);
  background-color: rgba(255, 255, 255, 0.35);
}

.title-container {
  position: relative;
  display: flex;
  flex-direction: column;
  padding-top: 10rpx;
}

.title-text {
  color: #FFFFFF;
  font-size: 40rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
  margin-bottom: 8rpx;
}

.title-underline {
  width: 40rpx;
  height: 6rpx;
  background: #FFFFFF;
  border-radius: 6rpx;
}

.action-area {
  margin-top: 20rpx;
  display: flex;
  align-items: center;
}
</style> 