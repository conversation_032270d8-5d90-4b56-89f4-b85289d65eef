<template>
  <view class="contract">
    <!-- Simple Contract Header -->
    <view class="header">
      <text class="title">燃气经营服务合同</text>
    </view>
    
    <!-- Contract Content - No scrolling -->
    <view class="content">
      <text class="paragraph">本合同由甲方（燃气供应商）与乙方（店铺经营者）共同签署，旨在规范燃气经营服务相关事项。</text>
      
      <text class="section">一、服务内容</text>
      <text class="paragraph">甲方向乙方提供燃气供应、设备维护、安全检查等服务。</text>
      
      <text class="section">二、安全责任</text>
      <text class="paragraph">乙方应当遵守燃气安全管理规定，配合甲方进行安全检查，发现安全隐患应立即整改。</text>
      
      <text class="section">三、费用结算</text>
      <text class="paragraph">乙方应当按照约定时间缴纳燃气费用，逾期未缴纳的，甲方有权暂停供气。</text>
      
      <text class="section">四、合同期限</text>
      <text class="paragraph">本合同有效期为一年，自签订之日起生效。</text>
    </view>
    
    <!-- Simple Signature Area -->
    <view class="signature">
      <view class="parties">
        <view class="party">
          <text class="party-label">甲方：燃气公司</text>
        </view>
        <view class="party">
          <text class="party-label">乙方：店铺经营者</text>
        </view>
      </view>
      
      <view class="sign-box">
        <text class="sign-label">点击签名确认合同</text>
        <view class="signature-area" @click="emit('sign')">
          <image 
            v-if="modelValue" 
            :src="modelValue" 
            class="signature-image" 
            mode="aspectFit"
          ></image>
          <view v-else class="signature-placeholder">
            <text>点击此处签名</text>
          </view>
        </view>
        <text class="date">{{ currentDateStr }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, defineProps, defineEmits, defineExpose } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['sign'])

// Computed
const currentDateStr = computed(() => {
  const date = new Date()
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
})

// 验证方法
const validateAll = () => {
  if (!props.modelValue) {
    uni.showToast({
      title: '请完成合同签名',
      icon: 'none'
    })
    return false
  }
  return true
}

// 暴露给父组件的方法
defineExpose({
  validateAll
})
</script>

<style scoped>
.contract {
  background-color: white;
  border-radius: 12rpx;
  /* margin: 20rpx; */
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
}

/* Simple header */
.header {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  text-align: center;
}

.title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

/* Content area - full display, no scrolling */
.content {
  padding: 30rpx;
}

.section {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin: 24rpx 0 12rpx 0;
  display: block;
}

.paragraph {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
  display: block;
}

/* Signature area */
.signature {
  padding: 30rpx;
  border-top: 1rpx solid #eee;
  background-color: #fafafa;
}

.parties {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.party {
  flex: 1;
}

.party-label {
  font-size: 26rpx;
  color: #666;
}

.sign-box {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sign-label {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.signature-area {
  width: 80%;
  height: 140rpx;
  border: 1rpx dashed #ddd;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
}

.signature-image {
  height: 90%;
  max-width: 90%;
}

.signature-placeholder {
  font-size: 26rpx;
  color: #999;
}

.date {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}
</style> 