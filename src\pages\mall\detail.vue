<template>
  <view class="container product-container">
    <swiper class="product-swiper" indicator-dots circular>
      <swiper-item v-for="(image, index) in product.images" :key="index">
        <image :src="image" mode="aspectFill" class="slide-image"></image>
      </swiper-item>
    </swiper>
    
    <view class="product-info">
      <view class="product-price">¥{{product.price}}</view>
      <view class="product-name">{{product.name}}</view>
      <view class="product-desc">{{product.description}}</view>
      <view class="product-meta">
        <text class="meta-item">销量: {{product.sales}}</text>
        <text class="meta-item">库存: {{product.stock}}</text>
      </view>
    </view>
    
    <view class="product-spec" @click="openSpecPopup">
      <view class="spec-title">规格</view>
      <view class="spec-content">
        <text>{{selectedSpec || '请选择规格'}}</text>
        <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
      </view>
    </view>
    
    <view class="product-detail">
      <view class="detail-tabs">
        <view 
          class="tab-item" 
          :class="{active: currentTab === 0}"
          @click="switchTab(0)"
        >
          商品详情
        </view>
        <view 
          class="tab-item" 
          :class="{active: currentTab === 1}"
          @click="switchTab(1)"
        >
          规格参数
        </view>
      </view>
      
      <view class="detail-content" v-if="currentTab === 0">
        <view class="detail-section">
          <view class="section-title">产品特点</view>
          <view class="feature-list">
            <view class="feature-item" v-for="(feature, index) in product.features" :key="index">
              <view class="feature-icon">√</view>
              <view class="feature-text">{{feature}}</view>
            </view>
          </view>
        </view>
        
        <view class="detail-images">
          <image 
            v-for="(image, index) in product.detailImages" 
            :key="index" 
            :src="image" 
            mode="widthFix"
            class="detail-image"
          ></image>
        </view>
      </view>
      
      <view class="detail-content" v-else>
        <view class="param-list">
          <view class="param-item" v-for="(param, index) in product.params" :key="index">
            <view class="param-label">{{param.label}}</view>
            <view class="param-value">{{param.value}}</view>
          </view>
        </view>
      </view>
    </view>
    
    <view class="bottom-bar">
      <view class="action-icons">
        <view class="action-item" @click="navigateToHome">
          <uni-icons type="home" size="24" color="#666"></uni-icons>
          <text>首页</text>
        </view>
        <view class="action-item" @click="navigateToCart">
          <uni-icons type="cart" size="24" color="#666"></uni-icons>
          <text>购物车</text>
          <view class="badge" v-if="cartCount > 0">{{cartCount}}</view>
        </view>
        <view class="action-item" @click="toggleFavorite">
          <uni-icons :type="isFavorite ? 'star-filled' : 'star'" size="24" :color="isFavorite ? '#ff9900' : '#666'"></uni-icons>
          <text>收藏</text>
        </view>
      </view>
      <view class="action-buttons">
        <button class="add-cart-btn" @click="openSpecPopup('cart')">加入购物车</button>
        <button class="buy-now-btn" @click="openSpecPopup('buy')">立即购买</button>
      </view>
    </view>
    
    <!-- 规格选择弹窗 -->
    <uni-popup ref="specPopup" type="bottom">
      <view class="spec-popup">
        <view class="popup-header">
          <image :src="product.images[0]" mode="aspectFill" class="product-thumb"></image>
          <view class="popup-product-info">
            <view class="popup-price">¥{{product.price}}</view>
            <view class="popup-stock">库存: {{product.stock}}件</view>
            <view class="popup-selected">已选: {{selectedSpec || '请选择规格'}}</view>
          </view>
          <view class="close-btn" @click="closeSpecPopup">
            <uni-icons type="close" size="20" color="#999"></uni-icons>
          </view>
        </view>
        
        <view class="popup-content">
          <view class="spec-group" v-for="(group, groupIndex) in product.specGroups" :key="groupIndex">
            <view class="spec-group-title">{{group.name}}</view>
            <view class="spec-options">
              <view 
                v-for="(option, optionIndex) in group.options" 
                :key="optionIndex"
                :class="['spec-option', {active: isSpecSelected(groupIndex, optionIndex)}]"
                @click="selectSpec(groupIndex, optionIndex)"
              >
                {{option}}
              </view>
            </view>
          </view>
          
          <view class="quantity-selector">
            <view class="selector-title">数量</view>
            <view class="selector-content">
              <view class="quantity-btn minus" @click="decreaseQuantity">-</view>
              <input type="number" v-model="quantity" class="quantity-input" />
              <view class="quantity-btn plus" @click="increaseQuantity">+</view>
            </view>
          </view>
        </view>
        
        <view class="popup-footer">
          <button class="confirm-btn" @click="confirmSpec">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      productId: null,
      product: {
        id: 1,
        name: '',
        description: '',
        price: 0,
        sales: 0,
        stock: 0,
        images: [],
        detailImages: [],
        features: [],
        params: [],
        specGroups: []
      },
      currentTab: 0,
      cartCount: 0,
      isFavorite: false,
      selectedSpecs: [], // 选中的规格选项索引数组
      selectedSpec: '', // 选中的规格文本描述
      quantity: 1, // 选择的数量
      actionType: 'cart' // 'cart' 加入购物车, 'buy' 立即购买
    }
  },
  
  onLoad(options) {
    if (options.id) {
      this.productId = options.id
      this.loadProductDetail()
    } else {
      uni.showToast({
        title: '商品ID错误',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  },
  
  methods: {
    // 加载商品详情
    loadProductDetail() {
      uni.showLoading({
        title: '加载中'
      })
      
      // 实际应用中应调用API获取商品详情
      // 这里模拟数据
      setTimeout(() => {
        this.product = {
          id: this.productId,
          name: '高压液化气软管',
          description: '加厚型高压液化气软管，安全耐用，适用于各类家用燃气灶具',
          price: 89.9,
          sales: 1254,
          stock: 500,
          images: [
            '/static/images/products/product-1.png',
            '/static/images/products/product-2.png',
            '/static/images/products/product-3.png'
          ],
          detailImages: [
            '/static/images/products/detail-1.png',
            '/static/images/products/detail-2.png'
          ],
          features: [
            '高强度耐压材质，安全可靠',
            '经过严格质检，符合国家安全标准',
            '适用于各种燃气灶具和气瓶连接',
            '使用寿命长，不易老化'
          ],
          params: [
            { label: '品牌', value: '安达燃气' },
            { label: '型号', value: 'RG-HSG-01' },
            { label: '材质', value: '增强型橡胶' },
            { label: '长度', value: '1.5m/2m/3m' },
            { label: '承压', value: '≤2.8MPa' },
            { label: '适用气源', value: '液化石油气/天然气' },
            { label: '生产标准', value: 'GB/T7304-2000' }
          ],
          specGroups: [
            {
              name: '长度',
              options: ['1.5米', '2米', '3米']
            },
            {
              name: '接头类型',
              options: ['普通接头', '快速接头']
            }
          ]
        }
        
        // 初始化规格选择数组
        this.selectedSpecs = new Array(this.product.specGroups.length).fill(-1)
        
        // 模拟获取购物车数量
        this.cartCount = 5
        
        uni.hideLoading()
      }, 1000)
    },
    
    // 切换详情选项卡
    switchTab(index) {
      this.currentTab = index
    },
    
    // 导航到首页
    navigateToHome() {
      uni.switchTab({
        url: '/pages/index/index'
      })
    },
    
    // 导航到购物车
    navigateToCart() {
      uni.navigateTo({
        url: '/pages/mall/cart'
      })
    },
    
    // 切换收藏状态
    toggleFavorite() {
      this.isFavorite = !this.isFavorite
      
      uni.showToast({
        title: this.isFavorite ? '收藏成功' : '取消收藏',
        icon: 'success'
      })
    },
    
    // 打开规格选择弹窗
    openSpecPopup(actionType) {
      if (actionType) {
        this.actionType = actionType
      }
      this.$refs.specPopup.open()
    },
    
    // 关闭规格选择弹窗
    closeSpecPopup() {
      this.$refs.specPopup.close()
    },
    
    // 判断规格是否已选中
    isSpecSelected(groupIndex, optionIndex) {
      return this.selectedSpecs[groupIndex] === optionIndex
    },
    
    // 选择规格
    selectSpec(groupIndex, optionIndex) {
      this.$set(this.selectedSpecs, groupIndex, optionIndex)
      this.updateSelectedSpecText()
    },
    
    // 更新已选规格文本
    updateSelectedSpecText() {
      const selected = []
      
      for (let i = 0; i < this.product.specGroups.length; i++) {
        const specIndex = this.selectedSpecs[i]
        if (specIndex !== -1) {
          selected.push(this.product.specGroups[i].options[specIndex])
        }
      }
      
      this.selectedSpec = selected.length > 0 ? selected.join('，') : ''
    },
    
    // 减少数量
    decreaseQuantity() {
      if (this.quantity > 1) {
        this.quantity--
      }
    },
    
    // 增加数量
    increaseQuantity() {
      if (this.quantity < this.product.stock) {
        this.quantity++
      } else {
        uni.showToast({
          title: '已达到库存上限',
          icon: 'none'
        })
      }
    },
    
    // 确认规格选择
    confirmSpec() {
      // 检查是否所有规格都已选择
      const allSelected = this.selectedSpecs.every(spec => spec !== -1)
      
      if (!allSelected) {
        uni.showToast({
          title: '请选择完整规格',
          icon: 'none'
        })
        return
      }
      
      // 根据不同操作类型执行不同操作
      if (this.actionType === 'cart') {
        this.addToCart()
      } else {
        this.buyNow()
      }
    },
    
    // 加入购物车
    addToCart() {
      uni.showLoading({
        title: '添加中'
      })
      
      // 实际应用中应调用API添加到购物车
      // 这里模拟异步请求
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '添加成功',
          icon: 'success'
        })
        
        // 更新购物车数量
        this.cartCount++
        
        // 关闭弹窗
        this.closeSpecPopup()
      }, 500)
    },
    
    // 立即购买
    buyNow() {
      const orderInfo = {
        productId: this.product.id,
        productName: this.product.name,
        price: this.product.price,
        quantity: this.quantity,
        specs: this.selectedSpec,
        image: this.product.images[0]
      }
      
      // 存储订单信息
      uni.setStorageSync('tempOrderInfo', orderInfo)
      
      // 跳转到结算页面
      uni.navigateTo({
        url: '/pages/mall/checkout?from=buy'
      })
      
      // 关闭弹窗
      this.closeSpecPopup()
    }
  }
}
</script>

<style scoped>
.product-container {
  padding-bottom: 120rpx;
}

.product-swiper {
  width: 100%;
  height: 750rpx;
}

.slide-image {
  width: 100%;
  height: 100%;
}

.product-info {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.product-price {
  color: #fa3534;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.product-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.product-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.product-meta {
  display: flex;
  font-size: 24rpx;
  color: #999;
}

.meta-item {
  margin-right: 30rpx;
}

.product-spec {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.spec-title {
  color: #666;
  font-size: 28rpx;
}

.spec-content {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}

.spec-content text {
  margin-right: 10rpx;
}

.product-detail {
  background-color: #fff;
}

.detail-tabs {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 30rpx;
  position: relative;
}

.tab-item.active {
  color: var(--primary-color);
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 120rpx;
  height: 4rpx;
  background-color: var(--primary-color);
}

.detail-content {
  padding: 30rpx;
}

.detail-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 10rpx;
  height: 30rpx;
  width: 6rpx;
  background-color: var(--primary-color);
  border-radius: 3rpx;
}

.feature-list {
  padding: 10rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.feature-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: var(--primary-color);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 15rpx;
}

.feature-text {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.6;
}

.detail-images {
  margin-top: 40rpx;
}

.detail-image {
  width: 100%;
  margin-bottom: 20rpx;
}

.param-list {
  padding: 10rpx;
}

.param-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.param-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #666;
}

.param-value {
  flex: 1;
  font-size: 28rpx;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  z-index: 99;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
}

.action-icons {
  display: flex;
  width: 40%;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  color: #666;
  position: relative;
}

.badge {
  position: absolute;
  top: 0;
  right: 15rpx;
  background-color: #fa3534;
  color: #fff;
  font-size: 20rpx;
  border-radius: 18rpx;
  min-width: 36rpx;
  height: 36rpx;
  line-height: 36rpx;
  text-align: center;
  padding: 0 6rpx;
}

.action-buttons {
  display: flex;
  width: 60%;
}

.add-cart-btn, .buy-now-btn {
  flex: 1;
  height: 100rpx;
  line-height: 100rpx;
  font-size: 30rpx;
  border-radius: 0;
}

.add-cart-btn {
  background-color: #ff9900;
  color: #fff;
}

.buy-now-btn {
  background-color: #fa3534;
  color: #fff;
}

/* 规格选择弹窗 */
.spec-popup {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
}

.popup-header {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}

.product-thumb {
  width: 180rpx;
  height: 180rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.popup-product-info {
  flex: 1;
}

.popup-price {
  color: #fa3534;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.popup-stock, .popup-selected {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 10rpx;
}

.popup-content {
  padding: 30rpx;
  max-height: 700rpx;
  overflow-y: auto;
}

.spec-group {
  margin-bottom: 30rpx;
}

.spec-group-title {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.spec-options {
  display: flex;
  flex-wrap: wrap;
}

.spec-option {
  padding: 10rpx 30rpx;
  border-radius: 6rpx;
  background-color: #f5f5f5;
  margin-right: 20rpx;
  margin-bottom: 15rpx;
  font-size: 26rpx;
}

.spec-option.active {
  background-color: rgba(41,121,255,0.1);
  color: var(--primary-color);
  border: 1rpx solid var(--primary-color);
}

.quantity-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}

.selector-title {
  font-size: 28rpx;
}

.selector-content {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background-color: #f5f5f5;
  font-size: 32rpx;
}

.quantity-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  border: 1rpx solid #f5f5f5;
  margin: 0 1rpx;
}

.popup-footer {
  padding: 20rpx 30rpx;
}

.confirm-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: var(--primary-color);
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
}
</style> 