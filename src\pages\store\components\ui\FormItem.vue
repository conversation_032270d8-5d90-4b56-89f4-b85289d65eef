<template>
  <view class="form-item">
    <text v-if="label" class="form-label">{{ label }}</text>
    <slot></slot>
  </view>
</template>

<script setup>
// 定义props
const props = defineProps({
  label: {
    type: String,
    default: ''
  }
})
</script>

<style scoped>
.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
  font-weight: 500;
}
</style> 