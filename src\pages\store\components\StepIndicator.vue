<template>
  <view class="step-container">
    <!-- Step indicators with separate line segments -->
    <view class="steps">
      <view 
        v-for="(step, index) in steps" 
        :key="index"
        class="step"
        :class="{ active: currentStep === index, completed: currentStep > index }"
      >
        <!-- Connection line to next step - always visible regardless of current step -->
        <view 
          v-if="index < steps.length - 1" 
          class="connection-line-container"
        >
          <view 
            class="connection-line"
            :class="{ 'completed-line': currentStep > index }"
          ></view>
        </view>
        
        <!-- Step marker -->
        <view class="marker-wrapper" @click="$emit('step-change', index)">
          <view class="marker">
            <text v-if="currentStep > index" class="check">✓</text>
            <text v-else class="number">{{ index + 1 }}</text>
          </view>
        </view>
        
        <!-- Step content -->
        <view class="step-content">
          <text class="step-title">{{ step }}</text>
          <text v-if="currentStep === index" class="step-subtitle">处理中</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
// 定义props
const props = defineProps({
  steps: {
    type: Array,
    required: true
  },
  currentStep: {
    type: Number,
    default: 0
  }
})

// 定义emits
const emit = defineEmits(['step-change'])
</script>

<style scoped>
.step-container {
  /* margin: 0 30rpx 30rpx 30rpx; */
  margin-bottom: 30rpx;
  background: white;
  border-radius: 16rpx;
  padding: 40rpx 30rpx 30rpx;
  position: relative;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: visible; /* Ensure lines aren't clipped */
}

/* Steps layout */
.steps {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  z-index: 2; /* Ensure steps are above lines */
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

/* Connection lines between steps */
.connection-line-container {
  position: absolute;
  top: 25rpx; /* Center with the marker */
  left: 50%; /* Start from the middle of current step */
  width: 100%; /* Full width to reach the next step */
  height: 1rpx;
  z-index: 1;
  pointer-events: none; /* Don't block clicks */
}

.connection-line {
  position: absolute;
  height: 100%;
  width: 40%; /* Shorter line */
  left: 30%; /* Center the line (100% - 40%)/2 = 30% */
  background-color: #EEEEEE;
  border-radius: 4rpx;
}

.completed-line {
  background: linear-gradient(90deg, var(--primary-color), #54BBFF);
  height: 2rpx; /* Make completed lines slightly more visible */
}

/* Marker styling */
.marker-wrapper {
  position: relative;
  z-index: 3; /* Ensure markers are above lines */
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border-radius: 50%;
  margin-bottom: 14rpx;
}

.marker {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  background-color: white;
  border: 2rpx solid #DDDDDD;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  color: #999999;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.step.active .marker {
  border-color: var(--primary-color);
  background-color: #E6F7FF;
  color: var(--primary-color);
  transform: scale(1.1);
  box-shadow: 0 0 0 6rpx rgba(24, 144, 255, 0.15);
}

.step.completed .marker {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.check {
  font-size: 22rpx;
  font-weight: bold;
}

.number {
  font-weight: 500;
}

/* Step content styling */
.step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 120rpx;
}

.step-title {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 6rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  text-align: center;
  transition: all 0.3s;
}

.step.active .step-title {
  color: var(--primary-color);
  font-weight: 500;
}

.step.completed .step-title {
  color: var(--primary-color);
}

.step-subtitle {
  font-size: 20rpx;
  color: var(--primary-color);
  opacity: 0.8;
}
</style> 