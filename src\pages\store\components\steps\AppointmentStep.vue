<template>
  <view class="appointment-step">


    <!-- 已有预约状态显示 -->
    <appointment-status
      v-if="hasExistingAppointment"
      :appointment="existingAppointment"
      @update-appointment="handleUpdateAppointment"
      @rebook="handleRebook"
    />

    <!-- 新建预约表单 -->
    <appointment-form
      v-else
      v-model="formData"
      ref="appointmentForm"
    />
  </view>
</template>

<script setup>
import { ref, computed, onMounted, defineProps, defineEmits, defineExpose } from 'vue'
import AppointmentForm from './AppointmentForm.vue'
import AppointmentStatus from './AppointmentStatus.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 组件引用
const appointmentForm = ref(null)

// 响应式数据
const currentTestMode = ref('cancelled') // 'confirmed', 'cancelled', 'form'

// 测试配置
const TEST_CONFIG = {
  hasAppointment: false, // 设置为 true 测试已有预约状态
  appointmentData: {
    appointmentId: 'APT20241201001',
    contactName: '张三',
    contactPhone: '13800138000',
    date: '2024-12-15',
    timeSlot: '上午 9:00-11:00',
    status: 'confirmed', // pending, confirmed, in-service, completed, cancelled
    remarks: '请提前电话联系',
    createTime: '2024-12-01T10:30:00.000Z',
    confirmedTime: '2024-12-01T11:00:00.000Z',
    serviceWorker: {
      name: '李师傅',
      phone: '13900139000'
    }
  },
  // 已取消预约的测试数据
  cancelledAppointmentData: {
    appointmentId: 'APT20241201002',
    contactName: '李四',
    contactPhone: '13900139000',
    date: '2024-12-20',
    timeSlot: '下午 2:00-4:00',
    status: 'cancelled',
    remarks: '需要安装在二楼',
    createTime: '2024-12-01T14:30:00.000Z',
    confirmedTime: '2024-12-01T15:00:00.000Z',
    cancelReason: '临时有事，无法按时到场',
    cancelledTime: '2024-12-02T09:30:00.000Z'
  }
}

// 计算属性
const formData = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

const hasExistingAppointment = computed(() => {
  // 优先检查实际数据
  if (props.modelValue.appointment && props.modelValue.appointment.status) {
    return true
  }
  // 测试模式
  return TEST_CONFIG.hasAppointment && currentTestMode.value !== 'form'
})

const existingAppointment = computed(() => {
  // 优先返回实际数据
  if (props.modelValue.appointment && props.modelValue.appointment.status) {
    return props.modelValue.appointment
  }
  // 测试模式
  if (currentTestMode.value === 'cancelled') {
    return TEST_CONFIG.cancelledAppointmentData
  }
  return TEST_CONFIG.appointmentData
})



// 事件处理方法
const handleUpdateAppointment = (updatedAppointment) => {
  const updatedForm = {
    ...formData.value,
    appointment: updatedAppointment
  }
  emit('update:modelValue', updatedForm)
}

const handleRebook = () => {
  // 清除现有预约数据，回到预约表单状态
  const updatedForm = {
    ...formData.value,
    appointment: null,
    // 清除之前的表单数据，让用户重新填写
    contactName: '',
    contactPhone: '',
    date: '',
    timeSlot: '',
    remarks: ''
  }
  emit('update:modelValue', updatedForm)

  // 在测试模式下切换到表单状态
  if (TEST_CONFIG.hasAppointment) {
    currentTestMode.value = 'form'
  }

  uni.showToast({
    title: '请重新填写预约信息',
    icon: 'none'
  })
}

// 生命周期
onMounted(() => {
  // 如果是测试模式且有预约，更新表单数据
  if (TEST_CONFIG.hasAppointment && !props.modelValue.appointment) {
    const updatedForm = {
      ...formData.value,
      appointment: TEST_CONFIG.appointmentData
    }
    emit('update:modelValue', updatedForm)
  }
})

// 验证方法
const validateAll = () => {
  // 如果已有预约，跳过验证
  if (hasExistingAppointment.value) {
    return true
  }

  // 调用表单组件的验证方法
  if (appointmentForm.value && typeof appointmentForm.value.validateAll === 'function') {
    return appointmentForm.value.validateAll()
  }

  // 备用验证逻辑
  uni.showToast({
    title: '请完善预约信息',
    icon: 'none'
  })
  return false
}

// 提交预约方法
const submitAppointment = async () => {
  // 如果已有预约，直接返回
  if (hasExistingAppointment.value) {
    return Promise.resolve()
  }

  // 调用表单组件的提交方法
  if (appointmentForm.value && typeof appointmentForm.value.submitAppointment === 'function') {
    return appointmentForm.value.submitAppointment()
  }

  // 备用提交逻辑
  throw new Error('预约表单组件不可用')
}

// 暴露给父组件的方法
defineExpose({
  validateAll,
  submitAppointment
})
</script>

<style scoped>
.appointment-step {
  min-height: 400rpx;
}


</style>