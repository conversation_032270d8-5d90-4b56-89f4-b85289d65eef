<template>
  <view class="container">
    <view class="header">
      <view class="title">隐患上报</view>
      <view class="subtitle">燃气安全隐患上报，保障您的安全</view>
    </view>
    
    <view class="form-container">
      <view class="form-item">
        <view class="label">问题类型 <text class="required">*</text></view>
        <picker 
          mode="selector" 
          :range="problemTypes" 
          :value="problemTypeIndex"
          @change="onProblemTypeChange"
        >
          <view class="picker">
            {{problemTypeIndex >= 0 ? problemTypes[problemTypeIndex] : '请选择问题类型'}}
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </picker>
      </view>
      
      <view class="form-item">
        <view class="label">详细描述 <text class="required">*</text></view>
        <textarea 
          v-model="form.description" 
          placeholder="请详细描述您发现的燃气安全隐患情况..."
          maxlength="500"
          class="textarea"
        />
        <view class="count">{{form.description.length}}/500</view>
      </view>
      
      <view class="form-item">
        <view class="label">联系人 <text class="required">*</text></view>
        <input 
          v-model="form.contactName" 
          placeholder="请输入您的姓名"
          class="input"
        />
      </view>
      
      <view class="form-item">
        <view class="label">联系电话 <text class="required">*</text></view>
        <input 
          v-model="form.contactPhone" 
          type="number"
          placeholder="请输入您的手机号码"
          class="input"
        />
      </view>
      
      <view class="form-item">
        <view class="label">选择位置 <text class="required">*</text></view>
        <view class="location-picker" @click="chooseLocation">
          <view class="location-content">
            <view class="location-icon">
              <uni-icons type="location" size="24" color="#2979ff"></uni-icons>
            </view>
            <view class="location-info">
              <view v-if="form.locationAddress" class="address-text">{{form.locationAddress}}</view>
              <view v-else class="placeholder">点击选择位置</view>
              <view v-if="form.locationName" class="location-name">{{form.locationName}}</view>
            </view>
            <view class="location-arrow">
              <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
            </view>
          </view>
        </view>
      </view>
      
      <view class="form-item">
        <view class="label">详细地址 <text class="required">*</text></view>
        <input 
          v-model="form.detailAddress" 
          placeholder="请输入详细门牌号、楼层、房间号等信息"
          class="input"
        />
      </view>
      
      <view class="form-item">
        <view class="label">上传图片</view>
        <view class="upload-container">
          <view class="upload-list">
            <view 
              class="upload-item" 
              v-for="(img, index) in form.images" 
              :key="index"
            >
              <image :src="img" mode="aspectFill" @click="previewImage(index)"></image>
              <view class="remove-btn" @click="removeImage(index)">×</view>
            </view>
            <view class="upload-btn" @click="chooseImage" v-if="form.images.length < 3">
              <uni-icons type="camera" size="30" color="#ccc"></uni-icons>
              <view class="upload-text">添加图片</view>
            </view>
          </view>
          <view class="upload-tips">最多可上传3张图片</view>
        </view>
      </view>
      
      <view class="form-item">
        <view class="label">期望处理时间</view>
        <picker 
          mode="selector" 
          :range="expectTimeOptions" 
          :value="expectTimeIndex"
          @change="onExpectTimeChange"
        >
          <view class="picker">
            {{expectTimeIndex >= 0 ? expectTimeOptions[expectTimeIndex] : '请选择期望处理时间'}}
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </picker>
      </view>
    </view>
    
    <view class="submit-section">
      <button 
        class="submit-btn" 
        @click="submitReport"
        :disabled="!isFormValid"
      >
        提交上报
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      problemTypes: [
        '燃气泄漏',
        '燃气管道破损',
        '燃气表异常',
        '燃气灶具故障',
        '燃气阀门损坏',
        '燃气异味',
        '其他问题'
      ],
      expectTimeOptions: [
        '立即处理',
        '2小时内',
        '4小时内',
        '当天处理',
        '次日处理'
      ],
      problemTypeIndex: -1,
      expectTimeIndex: -1,
      form: {
        problemType: '',
        description: '',
        contactName: '',
        contactPhone: '',
        locationAddress: '',
      locationName: '',
      detailAddress: '',
      latitude: '',
      longitude: '',
      images: [],
      expectTime: ''
      }
    }
  },
  
  computed: {
    isFormValid() {
      return this.form.problemType && 
             this.form.description && 
             this.form.contactName && 
             this.form.contactPhone && 
             this.form.locationAddress && 
             this.form.detailAddress
    }
  },
  
  methods: {
    onProblemTypeChange(e) {
      this.problemTypeIndex = parseInt(e.detail.value)
      this.form.problemType = this.problemTypes[this.problemTypeIndex]
    },
    
    onExpectTimeChange(e) {
      this.expectTimeIndex = parseInt(e.detail.value)
      this.form.expectTime = this.expectTimeOptions[this.expectTimeIndex]
    },
    
    chooseImage() {
      uni.chooseImage({
        count: 3 - this.form.images.length,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.form.images = [...this.form.images, ...res.tempFilePaths]
        }
      })
    },
    
    previewImage(index) {
      uni.previewImage({
        urls: this.form.images,
        current: index
      })
    },
    
    removeImage(index) {
      this.form.images.splice(index, 1)
    },
    
    chooseLocation() {
      uni.chooseLocation({
        success: (res) => {
          this.form.locationAddress = res.address
          this.form.locationName = res.name
          this.form.latitude = res.latitude.toString()
          this.form.longitude = res.longitude.toString()
        },
        fail: (err) => {
          if (err.errMsg !== 'chooseLocation:fail cancel') {
            uni.showToast({
              title: '获取位置失败，请重试',
              icon: 'none'
            })
          }
        }
      })
    },
    
    submitReport() {
      if (!this.isFormValid) {
        uni.showToast({
          title: '请完善必填信息',
          icon: 'none'
        })
        return
      }
      
      // 验证手机号
      if (!/^1[3-9]\d{9}$/.test(this.form.contactPhone)) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none'
        })
        return
      }
      
      uni.showLoading({
        title: '提交中...'
      })
      
      // 模拟提交
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '提交成功',
          icon: 'success'
        })
        
        // 重置表单
        this.resetForm()
        
        // 返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }, 2000)
    },
    
    resetForm() {
      this.problemTypeIndex = -1
      this.expectTimeIndex = -1
      this.form = {
        problemType: '',
        description: '',
        contactName: '',
        contactPhone: '',
        locationAddress: '',
        locationName: '',
        detailAddress: '',
        latitude: '',
        longitude: '',
        images: [],
        expectTime: ''
      }
    }
  }
}
</script>

<style scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background-color: #fff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #999;
}

.form-container {
  background-color: #fff;
  padding: 0 30rpx;
}

.form-item {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.required {
  color: #fa3534;
}

.picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
}

.input {
  width: 100%;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.location-picker {
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 30rpx;
}

.location-content {
  display: flex;
  align-items: center;
}

.location-icon {
  margin-right: 20rpx;
}

.location-info {
  flex: 1;
}

.address-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.placeholder {
  font-size: 28rpx;
  color: #999;
}

.location-name {
  font-size: 24rpx;
  color: #666;
}

.location-arrow {
  margin-left: 20rpx;
}

.upload-container {
  margin-top: 20rpx;
}

.upload-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.upload-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.upload-item image {
  width: 100%;
  height: 100%;
}

.remove-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0,0,0,0.5);
  color: #fff;
  font-size: 30rpx;
  text-align: center;
  line-height: 40rpx;
  border-radius: 50%;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #ccc;
}

.upload-text {
  font-size: 24rpx;
  margin-top: 10rpx;
}

.upload-tips {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.submit-section {
  padding: 40rpx 30rpx;
}

.submit-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #2979ff;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 45rpx;
}

.submit-btn[disabled] {
  background-color: #ccc;
}
</style>