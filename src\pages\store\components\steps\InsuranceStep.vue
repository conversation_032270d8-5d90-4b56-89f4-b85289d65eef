<template>
  <view class="insurance-step">
    <view class="step-header">
      <text class="step-title">保险选择</text>
      <text class="step-subtitle">为您的加气站选择合适的保险方案</text>
    </view>
    
    <view class="plans-container">
      <swiper 
        class="plans-swiper" 
        :autoplay="false" 
        :circular="true"
        previous-margin="60rpx"
        next-margin="180rpx"
        @change="onSwiperChange"
      >
        <swiper-item v-for="(plan, index) in insurancePlans" :key="index" class="swiper-item">
          <view 
            class="insurance-card"
            :class="{ selected: form.insurancePlan === index }"
            @click="selectPlan(index)"
          >
            <view class="card-tag" v-if="index === 1">推荐</view>
            <view class="card-content">
              <text class="plan-tier">{{ plan.name }}</text>
              <view class="price-container">
                <text class="price-currency">¥</text>
                <text class="price-amount">{{ plan.price }}</text>
                <text class="price-period">/年</text>
              </view>
              
              <view class="divider"></view>
              
              <view class="features-list">
                <view class="feature-item" v-for="(feature, idx) in plan.features" :key="idx">
                  <view class="check-icon">
                    <text class="icon-check">✓</text>
                  </view>
                  <text class="feature-text">{{ feature }}</text>
                </view>
              </view>
            </view>
            
            <view class="select-btn" :class="{ active: form.insurancePlan === index }">
              {{ form.insurancePlan === index ? '已选择' : '选择' }}
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
    
    <view class="form-wrapper">
      <view class="form-title">被保险人信息</view>
      
      <view class="input-group">
        <view class="input-label">姓名</view>
        <input 
          class="input-field" 
          v-model="form.insuranceName" 
          placeholder="请输入被保险人姓名"
          :placeholder-style="placeholderStyle"
        />
      </view>
      
      <view class="input-group">
        <view class="input-label">身份证号码</view>
        <input 
          class="input-field" 
          v-model="form.insuranceId" 
          placeholder="请输入身份证号码"
          :placeholder-style="placeholderStyle"
        />
        <view class="input-hint">请输入正确的18位身份证号码</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    modelValue: {
      type: Object,
      required: true
    },
    insurancePlans: {
      type: Array,
      default: () => []
    }
  },
  
  data() {
    return {
      placeholderStyle: 'color: #bbb; font-size: 28rpx;',
      currentSwiper: 0
    }
  },
  
  computed: {
    form: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  
  methods: {
    selectPlan(index) {
      const newForm = {...this.form, insurancePlan: index}
      this.$emit('update:modelValue', newForm)
    },
    
    onSwiperChange(e) {
      this.currentSwiper = e.detail.current
    }
  }
}
</script>

<style scoped>
.insurance-step {
  background-color: #f8f9fc;
  border-radius: 24rpx;
  padding: 40rpx 0;
  margin: 30rpx 0;
}

.step-header {
  padding: 0 30rpx 30rpx;
}

.step-title {
  font-size: 38rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.step-subtitle {
  font-size: 26rpx;
  color: #888;
  display: block;
}

.plans-container {
  margin-bottom: 50rpx;
  padding: 0;
}

.plans-swiper {
  height: 600rpx;
  width: calc(100% - 40rpx);
  margin: 0 auto;
}

.swiper-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 20rpx 0;
}

.insurance-card {
  width: 480rpx;
  height: calc(100% - 60rpx);
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid #eaeaea;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  margin: 0 20rpx;
}

.insurance-card.selected {
  border-color: var(--primary-color);
  box-shadow: 0 12rpx 30rpx rgba(48, 128, 255, 0.2);
  transform: translateY(-5rpx);
}

.card-tag {
  position: absolute;
  right: -10rpx;
  top: 20rpx;
  background: linear-gradient(135deg, var(--primary-color), #5a5cff);
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
  padding: 8rpx 30rpx;
  border-radius: 30rpx 0 0 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(48, 128, 255, 0.3);
  z-index: 1;
}

.card-content {
  padding: 30rpx;
  flex: 1;
}

.plan-tier {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.price-container {
  display: flex;
  align-items: baseline;
  margin-bottom: 25rpx;
}

.price-currency {
  font-size: 28rpx;
  color: #ff6b00;
  font-weight: 600;
}

.price-amount {
  font-size: 44rpx;
  color: #ff6b00;
  font-weight: 700;
  margin: 0 4rpx;
}

.price-period {
  font-size: 24rpx;
  color: #999;
}

.divider {
  height: 1rpx;
  background-color: #f0f0f0;
  margin: 20rpx 0;
}

.features-list {
  margin-top: 20rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background-color: rgba(52, 199, 89, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.icon-check {
  color: #34c759;
  font-size: 24rpx;
  font-weight: bold;
}

.feature-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  flex: 1;
}

.select-btn {
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  color: #666;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.select-btn.active {
  background-color: var(--primary-color);
  color: #fff;
}

.form-wrapper {
  padding: 0 30rpx;
  margin-top: 40rpx;
}

.form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  padding-left: 20rpx;
  border-left: 8rpx solid var(--primary-color);
}

.input-group {
  margin-bottom: 35rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.input-field {
  height: 90rpx;
  padding: 0 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  border: 1rpx solid #e0e0e0;
  font-size: 28rpx;
  color: #333;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

.input-field:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2rpx rgba(48, 128, 255, 0.1);
}

.input-hint {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  padding-left: 10rpx;
}
</style> 