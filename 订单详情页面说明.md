# 订单详情页面功能说明

## 概述

基于提交订单后的需求，我创建了一个完整的订单详情页面 (`src/pages/order/detail.vue`)，该页面展示订单的完整信息，包括订单状态、商品明细、配送信息、预约安装信息等。

## 主要功能特性

### 1. 订单状态展示
- **状态卡片**：采用渐变背景的状态卡片，直观显示当前订单状态
- **进度时间线**：根据业务类型（换气/租赁）显示不同的订单进度步骤
- **状态图标**：每个状态都有对应的emoji图标，提升用户体验

### 2. 订单基本信息
- 订单编号
- 下单时间
- 业务类型（换气/租赁）
- 联系人信息
- 配送地址

### 3. 商品明细展示
- **气瓶商品**：显示气瓶规格、数量、单价和小计
- **报警器押金**：租赁业务时显示报警器押金信息
- **配送费用**：立即配送时显示额外费用
- **订单总额**：清晰显示订单总金额

### 4. 配送信息
- 配送日期和时间段
- 配送状态（准备中/配送中/已送达）

### 5. 预约安装信息（租赁业务）
- 预约安装时间
- 预约状态（待确认/已确认/服务中/已完成）
- 安装师傅信息（姓名、电话）
- 联系师傅功能

### 6. 订单操作功能
- **取消订单**：待支付状态下可取消订单
- **修改配送**：已支付状态下可修改配送信息
- **确认安装完成**：租赁业务商品送达后确认安装完成
- **再次下单**：订单完成后可快速再次下单

## 参考的预约安装参数

基于 `AppointmentStatus.vue` 文件，订单详情页面包含了以下预约安装相关参数：

### 预约信息结构
```javascript
installationAppointment: {
  date: '2024-12-15',           // 预约日期
  week: '12月15日 周日',         // 格式化的日期显示
  time: '上午 9:00-11:00'        // 预约时间段
}
```

### 预约状态
- `pending`: 待确认
- `confirmed`: 已确认  
- `in-service`: 服务中
- `completed`: 已完成
- `cancelled`: 已取消

### 安装师傅信息
```javascript
installer: {
  name: '李师傅',              // 师傅姓名
  phone: '13900139000'         // 联系电话
}
```

## 页面路由配置

已在 `src/pages.json` 中添加了订单详情页面的路由配置：

```json
{
  "path": "detail",
  "style": {
    "navigationBarTitleText": "订单详情"
  }
}
```

## 页面跳转

从气瓶订单页面 (`gas-order.vue`) 提交订单成功后，会自动跳转到订单详情页面：

```javascript
uni.redirectTo({
  url: `/pages/order/detail?id=${orderId}`,
});
```

## 样式设计特点

### 1. 一致的设计语言
- 使用统一的卡片样式和圆角设计
- 采用项目统一的主色调 (`--primary-color`)
- 保持与其他页面一致的间距和字体大小

### 2. 响应式布局
- 适配不同屏幕尺寸
- 底部操作栏固定定位，支持安全区域适配

### 3. 状态区分
- 不同状态使用不同的颜色和图标
- 进度时间线清晰展示订单流程

### 4. 交互友好
- 重要操作有确认弹窗
- 联系师傅功能直接调用系统拨号
- 按钮有点击反馈效果

## 数据模拟

页面包含完整的模拟数据，展示了：
- 租赁业务的完整订单流程
- 包含报警器押金的订单
- 预约安装信息
- 安装师傅联系方式

## 扩展性

页面设计具有良好的扩展性：
- 支持不同业务类型的订单展示
- 可轻松添加新的订单状态
- 支持更多的订单操作功能
- 样式组件化，便于复用

## 使用方式

1. 在气瓶订单页面完成订单提交
2. 系统自动跳转到订单详情页面
3. 用户可查看完整的订单信息
4. 根据订单状态执行相应操作

该订单详情页面完整实现了订单信息展示和管理功能，为用户提供了清晰、友好的订单查看和操作体验。
